{"version": 2, "dgSpecHash": "BhQ6BeTVvCQ=", "success": true, "projectFilePath": "D:\\MP_NAE\\cy-nae-mall\\cy-nae-mall-web\\cy-nae-mall-server\\cy-nae-mall-server.csproj", "expectedPackageFiles": ["D:\\config\\.nuget\\packages\\microsoft.extensions.apidescription.server\\6.0.5\\microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512", "D:\\config\\.nuget\\packages\\microsoft.openapi\\1.6.14\\microsoft.openapi.1.6.14.nupkg.sha512", "D:\\config\\.nuget\\packages\\microsoft.visualstudio.azure.containers.tools.targets\\1.22.1\\microsoft.visualstudio.azure.containers.tools.targets.1.22.1.nupkg.sha512", "D:\\config\\.nuget\\packages\\swashbuckle.aspnetcore\\6.6.2\\swashbuckle.aspnetcore.6.6.2.nupkg.sha512", "D:\\config\\.nuget\\packages\\swashbuckle.aspnetcore.swagger\\6.6.2\\swashbuckle.aspnetcore.swagger.6.6.2.nupkg.sha512", "D:\\config\\.nuget\\packages\\swashbuckle.aspnetcore.swaggergen\\6.6.2\\swashbuckle.aspnetcore.swaggergen.6.6.2.nupkg.sha512", "D:\\config\\.nuget\\packages\\swashbuckle.aspnetcore.swaggerui\\6.6.2\\swashbuckle.aspnetcore.swaggerui.6.6.2.nupkg.sha512"], "logs": [{"code": "NU1900", "level": "Warning", "message": "获取包漏洞数据时出错: 无法加载源 https://api.nuget.org/v3/index.json 的服务索引。", "projectPath": "D:\\MP_NAE\\cy-nae-mall\\cy-nae-mall-web\\cy-nae-mall-server\\cy-nae-mall-server.csproj", "warningLevel": 1, "filePath": "D:\\MP_NAE\\cy-nae-mall\\cy-nae-mall-web\\cy-nae-mall-server\\cy-nae-mall-server.csproj", "targetGraphs": []}]}