<template>
  <view class="homepage">
    <!-- 轮播图区域 -->
    <view class="banner-section">
      <swiper
        class="banner-swiper"
        :indicator-dots="true"
        :autoplay="true"
        :interval="4000"
        :duration="800"
        circular
        indicator-color="rgba(255, 255, 255, 0.4)"
        indicator-active-color="#667eea"
      >
        <swiper-item
          v-for="(banner, index) in banners"
          :key="index"
          @click="onBannerClick(banner)"
        >
          <view class="banner-item">
            <image
              class="banner-image"
              :src="banner.image_url"
              mode="aspectFill"
            ></image>
            <view class="banner-overlay"></view>
            <view class="banner-content" v-if="banner.title">
              <text class="banner-title">{{ banner.title }}</text>
              <text class="banner-subtitle" v-if="banner.subtitle">{{
                banner.subtitle
              }}</text>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 快捷功能区域 -->
    <view class="quick-actions">
      <view
        class="quick-item"
        @click="navigateTo('/pages/installment/installment')"
      >
        <view class="quick-icon-wrapper installment">
          <image class="quick-icon" src="/static/index/pay.png"></image>
        </view>
        <text class="quick-text">分期购买</text>
        <text class="quick-desc">36期免息</text>
      </view>
      <view class="quick-item" @click="navigateTo('/pages/manual/manual')">
        <view class="quick-icon-wrapper manual">
          <image class="quick-icon" src="/static/index/book.png"></image>
        </view>
        <text class="quick-text">产品说明</text>
        <text class="quick-desc">详细指南</text>
      </view>
      <view class="quick-item" @click="navigateTo('/pages/warranty/warranty')">
        <view class="quick-icon-wrapper warranty">
          <image class="quick-icon" src="/static/index/quality.png"></image>
        </view>
        <text class="quick-text">质保服务</text>
        <text class="quick-desc">品质保障</text>
      </view>
      <view class="quick-item" @click="navigateTo('/pages/orders/orders')">
        <view class="quick-icon-wrapper orders">
          <image class="quick-icon" src="/static/index/connection.png"></image>
        </view>
        <text class="quick-text">我的订单</text>
        <text class="quick-desc">订单管理</text>
      </view>
    </view>

    <!-- 热销产品推荐区域 -->
    <view class="products-section">
      <view class="section-header">
        <view class="section-title-wrapper">
          <text class="section-title">热销推荐</text>
          <view class="title-decoration"></view>
        </view>
        <view class="section-more" @click="viewAllProducts">
          <text class="more-text">查看更多</text>
          <text class="more-arrow">→</text>
        </view>
      </view>
      <scroll-view
        class="products-scroll"
        scroll-x="true"
        show-scrollbar="false"
      >
        <view class="products-list">
          <view
            class="product-item"
            v-for="(product, index) in hotProducts"
            :key="index"
            @click="viewProductDetail(product)"
          >
            <view class="product-image-wrapper">
              <image
                class="product-image"
                :src="product.images[0]"
                mode="aspectFill"
              ></image>
              <view
                class="product-badge"
                v-if="product.original_price > product.price"
              >
                <text class="badge-text">特惠</text>
              </view>
            </view>
            <view class="product-info">
              <text class="product-name">{{ product.name }}</text>
              <view class="product-price">
                <text class="current-price">¥{{ product.price }}</text>
                <text
                  class="original-price"
                  v-if="product.original_price > product.price"
                  >¥{{ product.original_price }}</text
                >
              </view>
              <view class="product-footer">
                <text class="sales-text">已售{{ product.sales_count }}件</text>
                <view class="add-cart-btn">
                  <text class="add-cart-text">+</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 品牌特色区域 -->
    <view class="brand-section">
      <view class="brand-header">
        <text class="brand-title">传艺品质</text>
        <text class="brand-subtitle">世界品牌，中国传艺</text>
      </view>
      <view class="brand-features">
        <view class="feature-item">
          <view class="feature-icon">🏆</view>
          <text class="feature-text">品质保证</text>
        </view>
        <view class="feature-item">
          <view class="feature-icon">🚚</view>
          <text class="feature-text">快速配送</text>
        </view>
        <view class="feature-item">
          <view class="feature-icon">💳</view>
          <text class="feature-text">分期免息</text>
        </view>
        <view class="feature-item">
          <view class="feature-icon">🛡️</view>
          <text class="feature-text">售后保障</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 轮播图数据
      banners: [
        {
          image_url: "/static/banners/banner1.jpg",
          title: "新品上市",
          subtitle: "智能科技，引领未来",
          link_url: "/pages/product/detail?id=1",
          link_type: 1,
        },
        {
          image_url: "/static/banners/banner2.jpg",
          title: "限时优惠",
          subtitle: "全场8折，错过再等一年",
          link_url: "/pages/activity/sale",
          link_type: 2,
        },
        {
          image_url: "/static/banners/banner3.jpg",
          title: "品质保证",
          subtitle: "世界品牌，中国传艺",
          link_url: "/pages/guarantee",
          link_type: 2,
        },
      ],
      // 热销产品数据
      hotProducts: [
        {
          id: 1,
          name: "智能手机",
          price: 2999.0,
          original_price: 3299.0,
          sales_count: 50,
          images: ["/static/products/phone1.jpg"],
        },
        {
          id: 2,
          name: "无线耳机",
          price: 299.0,
          original_price: 399.0,
          sales_count: 30,
          images: ["/static/products/earphone1.jpg"],
        },
        {
          id: 3,
          name: "智能手表",
          price: 1299.0,
          original_price: 1499.0,
          sales_count: 25,
          images: ["/static/products/watch1.jpg"],
        },
        {
          id: 4,
          name: "平板电脑",
          price: 1999.0,
          original_price: 2299.0,
          sales_count: 15,
          images: ["/static/products/tablet1.jpg"],
        },
      ],
    };
  },
  onLoad() {
    this.loadBanners();
    this.loadHotProducts();
  },
  methods: {
    // 加载轮播图数据
    loadBanners() {
      // TODO: 调用后端API获取轮播图数据
      console.log("加载轮播图数据");
    },

    // 加载热销产品数据
    loadHotProducts() {
      // TODO: 调用后端API获取热销产品数据
      console.log("加载热销产品数据");
    },

    // 轮播图点击事件
    onBannerClick(banner) {
      if (banner.link_type === 1) {
        // 跳转到商品详情
        uni.navigateTo({
          url: banner.link_url,
        });
      } else if (banner.link_type === 2) {
        // 跳转到页面
        uni.navigateTo({
          url: banner.link_url,
        });
      } else if (banner.link_type === 3) {
        // 外部链接
        // #ifdef H5
        window.open(banner.link_url);
        // #endif
      }
    },

    // 导航到指定页面
    navigateTo(url) {
      uni.navigateTo({
        url: url,
      });
    },

    // 查看商品详情
    viewProductDetail(product) {
      uni.navigateTo({
        url: `/pages/product/detail?id=${product.id}`,
      });
    },

    // 查看更多产品
    viewAllProducts() {
      uni.navigateTo({
        url: "/pages/products/list",
      });
    },
  },
};
</script>

<style scoped>
.homepage {
  background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
  min-height: 100vh;
}

/* 轮播图区域 */
.banner-section {
  width: 100%;
  height: 450rpx;
  position: relative;
  margin-bottom: 30rpx;
}

.banner-swiper {
  width: 100%;
  height: 100%;
  border-radius: 0 0 40rpx 40rpx;
  overflow: hidden;
}

.banner-item {
  position: relative;
  width: 100%;
  height: 100%;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.3) 70%,
    rgba(0, 0, 0, 0.6) 100%
  );
}

.banner-content {
  position: absolute;
  bottom: 40rpx;
  left: 40rpx;
  right: 40rpx;
  z-index: 2;
}

.banner-title {
  color: white;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.banner-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 26rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

/* 快捷功能区域 */
.quick-actions {
  display: flex;
  justify-content: space-between;
  background-color: white;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.quick-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

.quick-icon-wrapper {
  width: 70rpx;
  height: 70rpx;
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.quick-icon-wrapper:active {
  transform: scale(0.95);
  box-shadow: 0 3rpx 12rpx rgba(0, 0, 0, 0.15);
}

.quick-icon-wrapper.installment {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.quick-icon-wrapper.manual {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.quick-icon-wrapper.warranty {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.quick-icon-wrapper.orders {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.quick-icon {
  width: 36rpx;
  height: 36rpx;
  filter: brightness(0) invert(1);
}

.quick-text {
  font-size: 22rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 4rpx;
  text-align: center;
  line-height: 1.2;
}

.quick-desc {
  font-size: 18rpx;
  color: #999;
  text-align: center;
  line-height: 1.2;
}

/* 热销产品推荐区域 */
.products-section {
  background-color: white;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title-wrapper {
  display: flex;
  align-items: center;
  position: relative;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 15rpx;
}

.title-decoration {
  width: 40rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 3rpx;
}

.section-more {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background-color: #f8f9fa;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.section-more:active {
  background-color: #e9ecef;
  transform: scale(0.95);
}

.more-text {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
}

.more-arrow {
  font-size: 24rpx;
  color: #667eea;
  font-weight: bold;
}

.products-scroll {
  width: 100%;
  white-space: nowrap;
}

.products-list {
  display: flex;
  gap: 20rpx;
  padding-bottom: 10rpx;
}

.product-item {
  width: 280rpx;
  background-color: white;
  border-radius: 20rpx;
  overflow: hidden;
  transition: all 0.3s ease;
  flex-shrink: 0;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
}

.product-item:active {
  transform: scale(0.98);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.12);
}

.product-image-wrapper {
  position: relative;
  width: 100%;
  height: 200rpx;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.product-badge {
  position: absolute;
  top: 12rpx;
  left: 12rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
  border-radius: 12rpx;
  padding: 4rpx 12rpx;
  z-index: 2;
}

.badge-text {
  font-size: 20rpx;
  color: white;
  font-weight: 600;
}

.product-info {
  padding: 20rpx;
}

.product-name {
  font-size: 26rpx;
  color: #333;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.product-price {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.current-price {
  font-size: 30rpx;
  color: #ff6b6b;
  font-weight: bold;
  margin-right: 10rpx;
}

.original-price {
  font-size: 22rpx;
  color: #999;
  text-decoration: line-through;
}

.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sales-text {
  font-size: 22rpx;
  color: #999;
}

.add-cart-btn {
  width: 50rpx;
  height: 50rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 25rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.add-cart-btn:active {
  transform: scale(0.9);
}

.add-cart-text {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  line-height: 1;
}

/* 品牌特色区域 */
.brand-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  position: relative;
  overflow: hidden;
}

.brand-section::before {
  content: "";
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 70%
  );
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.brand-header {
  text-align: center;
  margin-bottom: 30rpx;
  position: relative;
  z-index: 2;
}

.brand-title {
  font-size: 36rpx;
  color: white;
  font-weight: bold;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.brand-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.brand-features {
  display: flex;
  justify-content: space-between;
  position: relative;
  z-index: 2;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.feature-icon {
  font-size: 40rpx;
  margin-bottom: 12rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
}

.feature-text {
  font-size: 22rpx;
  color: white;
  text-align: center;
  font-weight: 500;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
</style>
