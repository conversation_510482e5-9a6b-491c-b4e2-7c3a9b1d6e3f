<template>
  <view class="homepage">
    <!-- 轮播图区域 -->
    <view class="banner-section">
      <swiper
        class="banner-swiper"
        :indicator-dots="true"
        :autoplay="true"
        :interval="4000"
        :duration="800"
        circular
        indicator-color="rgba(255, 255, 255, 0.4)"
        indicator-active-color="#667eea"
      >
        <swiper-item
          v-for="(banner, index) in banners"
          :key="index"
          @click="onBannerClick(banner)"
        >
          <view class="banner-item">
            <image
              class="banner-image"
              :src="banner.image_url"
              mode="aspectFill"
            ></image>
            <view class="banner-overlay"></view>
            <view class="banner-content" v-if="banner.title">
              <text class="banner-title">{{ banner.title }}</text>
              <text class="banner-subtitle" v-if="banner.subtitle">{{
                banner.subtitle
              }}</text>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 快捷功能区域 -->
    <view class="quick-actions">
      <view
        class="quick-item"
        @click="navigateTo('/pages/installment/installment')"
      >
        <view class="quick-icon-wrapper installment">
          <image class="quick-icon" src="/static/index/pay.png"></image>
        </view>
        <text class="quick-text">分期购买</text>
        <text class="quick-desc">36期免息</text>
      </view>
      <view class="quick-item" @click="navigateTo('/pages/manual/manual')">
        <view class="quick-icon-wrapper manual">
          <image class="quick-icon" src="/static/index/book.png"></image>
        </view>
        <text class="quick-text">产品说明</text>
        <text class="quick-desc">详细指南</text>
      </view>
      <view class="quick-item" @click="navigateTo('/pages/warranty/warranty')">
        <view class="quick-icon-wrapper warranty">
          <image class="quick-icon" src="/static/index/quality.png"></image>
        </view>
        <text class="quick-text">质保服务</text>
        <text class="quick-desc">品质保障</text>
      </view>
      <view class="quick-item" @click="navigateTo('/pages/orders/orders')">
        <view class="quick-icon-wrapper orders">
          <image class="quick-icon" src="/static/index/connection.png"></image>
        </view>
        <text class="quick-text">我的订单</text>
        <text class="quick-desc">订单管理</text>
      </view>
    </view>

    <!-- 热销产品推荐区域 -->
    <view class="products-section">
      <view class="section-header">
        <view class="section-title-wrapper">
          <text class="section-title">热销推荐</text>
        </view>
      </view>
      <view class="products-grid">
        <view
          class="product-item"
          v-for="(product, index) in hotProducts.slice(0, 4)"
          :key="index"
          @click="viewProductDetail(product)"
        >
          <view class="product-image-wrapper">
            <image
              class="product-image"
              :src="product.images[0]"
              mode="aspectFill"
            ></image>
            <view
              class="product-badge"
              v-if="product.original_price > product.price"
            >
              <text class="badge-text">热销</text>
            </view>
          </view>
          <view class="product-info">
            <text class="product-name">{{ product.name }}</text>
            <text class="product-desc">{{
              product.description || "精选优质商品，品质保证"
            }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      banners: [
        {
          image_url: "/static/banners/banner1.jpg",
          title: "新品上市",
          subtitle: "智能科技，引领未来",
          link_url: "/pages/product/detail?id=1",
          link_type: 1,
        },
      ],
      hotProducts: [
        {
          id: 1,
          name: "智能手机",
          description: "5G旗舰机型，拍照更清晰",
          price: 2999.0,
          original_price: 3299.0,
          sales_count: 50,
          images: ["/static/products/phone1.jpg"],
        },
        {
          id: 2,
          name: "无线耳机",
          description: "降噪技术，音质出众",
          price: 299.0,
          original_price: 399.0,
          sales_count: 30,
          images: ["/static/products/earphone1.jpg"],
        },
        {
          id: 3,
          name: "智能手表",
          description: "健康监测，运动伴侣",
          price: 1299.0,
          original_price: 1499.0,
          sales_count: 25,
          images: ["/static/products/watch1.jpg"],
        },
        {
          id: 4,
          name: "平板电脑",
          description: "轻薄便携，办公娱乐",
          price: 1999.0,
          original_price: 2299.0,
          sales_count: 15,
          images: ["/static/products/tablet1.jpg"],
        },
        {
          id: 5,
          name: "蓝牙音箱",
          description: "立体声环绕，震撼音效",
          price: 199.0,
          original_price: 299.0,
          sales_count: 40,
          images: ["/static/products/speaker1.jpg"],
        },
        {
          id: 6,
          name: "智能家居",
          description: "语音控制，智慧生活",
          price: 599.0,
          original_price: 799.0,
          sales_count: 20,
          images: ["/static/products/smart1.jpg"],
        },
      ],
    };
  },
  onLoad() {
    this.loadBanners();
    this.loadHotProducts();
  },
  methods: {
    // 加载轮播图数据
    loadBanners() {
      // TODO: 调用后端API获取轮播图数据
      console.log("加载轮播图数据");
    },

    // 加载热销产品数据
    loadHotProducts() {
      // TODO: 调用后端API获取热销产品数据
      console.log("加载热销产品数据");
    },

    // 轮播图点击事件
    onBannerClick(banner) {
      if (banner.link_type === 1) {
        // 跳转到商品详情
        uni.navigateTo({
          url: banner.link_url,
        });
      } else if (banner.link_type === 2) {
        // 跳转到页面
        uni.navigateTo({
          url: banner.link_url,
        });
      } else if (banner.link_type === 3) {
        // 外部链接
        // #ifdef H5
        window.open(banner.link_url);
        // #endif
      }
    },

    // 导航到指定页面
    navigateTo(url) {
      uni.navigateTo({
        url: url,
      });
    },

    // 查看商品详情
    viewProductDetail(product) {
      uni.navigateTo({
        url: `/pages/product/detail?id=${product.id}`,
      });
    },

    // 查看更多产品
    viewAllProducts() {
      uni.navigateTo({
        url: "/pages/products/list",
      });
    },
  },
};
</script>

<style scoped>
.homepage {
  background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
  min-height: 100vh;
}

/* 轮播图区域 */
.banner-section {
  width: 100%;
  height: 450rpx;
  position: relative;
  margin-bottom: 30rpx;
}

.banner-swiper {
  width: 100%;
  height: 100%;
  border-radius: 0 0 40rpx 40rpx;
  overflow: hidden;
}

.banner-item {
  position: relative;
  width: 100%;
  height: 100%;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.3) 70%,
    rgba(0, 0, 0, 0.6) 100%
  );
}

.banner-content {
  position: absolute;
  bottom: 40rpx;
  left: 40rpx;
  right: 40rpx;
  z-index: 2;
}

.banner-title {
  color: white;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.banner-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 26rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

/* 快捷功能区域 */
.quick-actions {
  display: flex;
  justify-content: space-between;
  background-color: white;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.quick-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

.quick-icon-wrapper {
  width: 70rpx;
  height: 70rpx;
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.quick-icon-wrapper:active {
  transform: scale(0.95);
  box-shadow: 0 3rpx 12rpx rgba(0, 0, 0, 0.15);
}

.quick-icon-wrapper.installment {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.quick-icon-wrapper.manual {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.quick-icon-wrapper.warranty {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.quick-icon-wrapper.orders {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.quick-icon {
  width: 36rpx;
  height: 36rpx;
  filter: brightness(0) invert(1);
}

.quick-text {
  font-size: 22rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 4rpx;
  text-align: center;
  line-height: 1.2;
}

.quick-desc {
  font-size: 18rpx;
  color: #999;
  text-align: center;
  line-height: 1.2;
}

/* 热销产品推荐区域 */
.products-section {
  background-color: white;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title-wrapper {
  display: flex;
  align-items: center;
  position: relative;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 15rpx;
}

.title-decoration {
  width: 40rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 3rpx;
}

.section-more {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background-color: #f8f9fa;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.section-more:active {
  background-color: #e9ecef;
  transform: scale(0.95);
}

.more-text {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
}

.more-arrow {
  font-size: 24rpx;
  color: #667eea;
  font-weight: bold;
}

.products-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  padding-bottom: 10rpx;
}

.product-item {
  background-color: white;
  border-radius: 20rpx;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
  position: relative;
}

.product-item:active {
  transform: scale(0.98);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.12);
}

.product-image-wrapper {
  position: relative;
  width: 100%;
  height: 160rpx;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.product-item:active .product-image {
  transform: scale(1.05);
}

.product-badge {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
  border-radius: 10rpx;
  padding: 4rpx 10rpx;
  z-index: 2;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
}

.badge-text {
  font-size: 18rpx;
  color: white;
  font-weight: 600;
}

.product-info {
  padding: 16rpx;
  height: 120rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-desc {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 品牌特色区域 */
.brand-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  position: relative;
  overflow: hidden;
}

.brand-section::before {
  content: "";
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 70%
  );
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.brand-header {
  text-align: center;
  margin-bottom: 30rpx;
  position: relative;
  z-index: 2;
}

.brand-title {
  font-size: 36rpx;
  color: white;
  font-weight: bold;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.brand-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.brand-features {
  display: flex;
  justify-content: space-between;
  position: relative;
  z-index: 2;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.feature-icon {
  font-size: 40rpx;
  margin-bottom: 12rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
}

.feature-text {
  font-size: 22rpx;
  color: white;
  text-align: center;
  font-weight: 500;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
</style>
