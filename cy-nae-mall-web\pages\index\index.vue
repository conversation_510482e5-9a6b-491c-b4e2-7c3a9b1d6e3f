<template>
	<view class="homepage">
		<!-- 轮播图区域 -->
		<view class="banner-section">
			<swiper class="banner-swiper" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500" circular>
				<swiper-item v-for="(banner, index) in banners" :key="index" @click="onBannerClick(banner)">
					<image class="banner-image" :src="banner.image_url" mode="aspectFill"></image>
					<view class="banner-title" v-if="banner.title">{{banner.title}}</view>
				</swiper-item>
			</swiper>
		</view>

		<!-- 功能模块区域 -->
		<view class="function-section">
			<view class="function-title">功能服务</view>
			<view class="function-grid">
				<view class="function-item" @click="navigateTo('/pages/installment/installment')">
					<image class="function-icon" src="/static/index/pay.png"></image>
					<text class="function-text">分期购买</text>
				</view>
				<view class="function-item" @click="navigateTo('/pages/manual/manual')">
					<image class="function-icon" src="/static/index/book.png"></image>
					<text class="function-text">产品说明</text>
				</view>
				<view class="function-item" @click="navigateTo('/pages/warranty/warranty')">
					<image class="function-icon" src="/static/index/quality.png"></image>
					<text class="function-text">质保服务</text>
				</view>
				<view class="function-item" @click="navigateTo('/pages/orders/orders')">
					<image class="function-icon" src="/static/index/connection.png"></image>
					<text class="function-text">我的订单</text>
				</view>
			</view>
		</view>

		<!-- 热销产品推荐区域 -->
		<view class="products-section">
			<view class="section-header">
				<text class="section-title">热销推荐</text>
				<text class="section-more" @click="viewAllProducts">查看更多 ></text>
			</view>
			<scroll-view class="products-scroll" scroll-x="true" show-scrollbar="false">
				<view class="products-list">
					<view class="product-item" v-for="(product, index) in hotProducts" :key="index" @click="viewProductDetail(product)">
						<image class="product-image" :src="product.images[0]" mode="aspectFill"></image>
						<view class="product-info">
							<text class="product-name">{{product.name}}</text>
							<view class="product-price">
								<text class="current-price">¥{{product.price}}</text>
								<text class="original-price" v-if="product.original_price > product.price">¥{{product.original_price}}</text>
							</view>
							<view class="product-sales">
								<text class="sales-text">已售{{product.sales_count}}件</text>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 轮播图数据
				banners: [
					{
						image_url: '/static/banners/banner1.jpg',
						title: '新品上市',
						link_url: '/pages/product/detail?id=1',
						link_type: 1
					},
					{
						image_url: '/static/banners/banner2.jpg',
						title: '限时优惠',
						link_url: '/pages/activity/sale',
						link_type: 2
					},
					{
						image_url: '/static/banners/banner3.jpg',
						title: '品质保证',
						link_url: '/pages/guarantee',
						link_type: 2
					}
				],
				// 热销产品数据
				hotProducts: [
					{
						id: 1,
						name: '智能手机',
						price: 2999.00,
						original_price: 3299.00,
						sales_count: 50,
						images: ['/static/products/phone1.jpg']
					},
					{
						id: 2,
						name: '无线耳机',
						price: 299.00,
						original_price: 399.00,
						sales_count: 30,
						images: ['/static/products/earphone1.jpg']
					},
					{
						id: 3,
						name: '智能手表',
						price: 1299.00,
						original_price: 1499.00,
						sales_count: 25,
						images: ['/static/products/watch1.jpg']
					},
					{
						id: 4,
						name: '平板电脑',
						price: 1999.00,
						original_price: 2299.00,
						sales_count: 15,
						images: ['/static/products/tablet1.jpg']
					}
				]
			}
		},
		onLoad() {
			this.loadBanners();
			this.loadHotProducts();
		},
		methods: {
			// 加载轮播图数据
			loadBanners() {
				// TODO: 调用后端API获取轮播图数据
				console.log('加载轮播图数据');
			},
			
			// 加载热销产品数据
			loadHotProducts() {
				// TODO: 调用后端API获取热销产品数据
				console.log('加载热销产品数据');
			},
			
			// 轮播图点击事件
			onBannerClick(banner) {
				if (banner.link_type === 1) {
					// 跳转到商品详情
					uni.navigateTo({
						url: banner.link_url
					});
				} else if (banner.link_type === 2) {
					// 跳转到页面
					uni.navigateTo({
						url: banner.link_url
					});
				} else if (banner.link_type === 3) {
					// 外部链接
					// #ifdef H5
					window.open(banner.link_url);
					// #endif
				}
			},
			
			// 导航到指定页面
			navigateTo(url) {
				uni.navigateTo({
					url: url
				});
			},
			
			// 查看商品详情
			viewProductDetail(product) {
				uni.navigateTo({
					url: `/pages/product/detail?id=${product.id}`
				});
			},
			
			// 查看更多产品
			viewAllProducts() {
				uni.navigateTo({
					url: '/pages/products/list'
				});
			}
		}
	}
</script>

<style scoped>
	.homepage {
		background-color: #f8f8f8;
		min-height: 100vh;
	}

	/* 轮播图区域 */
	.banner-section {
		width: 100%;
		height: 400rpx;
		position: relative;
	}

	.banner-swiper {
		width: 100%;
		height: 100%;
	}

	.banner-image {
		width: 100%;
		height: 100%;
	}

	.banner-title {
		position: absolute;
		bottom: 20rpx;
		left: 20rpx;
		background-color: rgba(0, 0, 0, 0.5);
		color: white;
		padding: 10rpx 20rpx;
		border-radius: 10rpx;
		font-size: 28rpx;
	}

	/* 功能模块区域 */
	.function-section {
		background-color: white;
		margin: 20rpx;
		border-radius: 20rpx;
		padding: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.function-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 30rpx;
	}

	.function-grid {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
	}

	.function-item {
		width: 45%;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 20rpx;
		margin-bottom: 20rpx;
		border-radius: 15rpx;
		background-color: #f8f9fa;
		transition: all 0.3s ease;
	}

	.function-item:active {
		background-color: #e9ecef;
		transform: scale(0.95);
	}

	.function-icon {
		width: 80rpx;
		height: 80rpx;
		margin-bottom: 15rpx;
	}

	.function-text {
		font-size: 26rpx;
		color: #666;
		text-align: center;
	}

	/* 热销产品推荐区域 */
	.products-section {
		background-color: white;
		margin: 20rpx;
		border-radius: 20rpx;
		padding: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}

	.section-more {
		font-size: 26rpx;
		color: #999;
	}

	.products-scroll {
		width: 100%;
		white-space: nowrap;
	}

	.products-list {
		display: flex;
		gap: 20rpx;
	}

	.product-item {
		width: 280rpx;
		background-color: #f8f9fa;
		border-radius: 15rpx;
		overflow: hidden;
		transition: all 0.3s ease;
		flex-shrink: 0;
	}

	.product-item:active {
		transform: scale(0.95);
	}

	.product-image {
		width: 100%;
		height: 200rpx;
	}

	.product-info {
		padding: 20rpx;
	}

	.product-name {
		font-size: 26rpx;
		color: #333;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
		line-height: 1.4;
		margin-bottom: 10rpx;
	}

	.product-price {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx;
	}

	.current-price {
		font-size: 28rpx;
		color: #ff6b6b;
		font-weight: bold;
		margin-right: 10rpx;
	}

	.original-price {
		font-size: 22rpx;
		color: #999;
		text-decoration: line-through;
	}

	.product-sales {
		display: flex;
		align-items: center;
	}

	.sales-text {
		font-size: 22rpx;
		color: #999;
	}
</style>
