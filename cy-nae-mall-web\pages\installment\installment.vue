<template>
	<view class="installment-page">
		<!-- 顶部装饰 -->
		<view class="header-section">
			<image class="installment-icon" src="/static/index/pay.png"></image>
			<text class="page-title">分期购买</text>
			<text class="page-subtitle">36期免息，轻松购物</text>
		</view>

		<!-- 分期说明卡片 -->
		<view class="info-card">
			<view class="card-header">
				<image class="bank-logo" src="/static/payment/jsbank.png"></image>
				<view class="bank-info">
					<text class="bank-name">江苏银行消费贷</text>
					<text class="bank-desc">专业金融服务，安全可靠</text>
				</view>
			</view>
			
			<view class="installment-details">
				<view class="detail-item">
					<image class="detail-icon" src="/static/icons/period.png"></image>
					<view class="detail-content">
						<text class="detail-title">36期免息</text>
						<text class="detail-desc">最长36期分期，0利息0手续费</text>
					</view>
				</view>
				
				<view class="detail-item">
					<image class="detail-icon" src="/static/icons/approval.png"></image>
					<view class="detail-content">
						<text class="detail-title">快速审批</text>
						<text class="detail-desc">在线申请，秒级审批通过</text>
					</view>
				</view>
				
				<view class="detail-item">
					<image class="detail-icon" src="/static/icons/security.png"></image>
					<view class="detail-content">
						<text class="detail-title">安全支付</text>
						<text class="detail-desc">银行级安全保障，资金安全</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 使用流程 -->
		<view class="process-card">
			<text class="card-title">使用流程</text>
			<view class="process-steps">
				<view class="step-item">
					<view class="step-number">1</view>
					<view class="step-content">
						<text class="step-title">扫码进入</text>
						<text class="step-desc">扫描门店收款码进入商品页面</text>
					</view>
				</view>
				
				<view class="step-item">
					<view class="step-number">2</view>
					<view class="step-content">
						<text class="step-title">选择商品</text>
						<text class="step-desc">浏览并选择心仪的商品</text>
					</view>
				</view>
				
				<view class="step-item">
					<view class="step-number">3</view>
					<view class="step-content">
						<text class="step-title">申请分期</text>
						<text class="step-desc">填写分期信息并提交申请</text>
					</view>
				</view>
				
				<view class="step-item">
					<view class="step-number">4</view>
					<view class="step-content">
						<text class="step-title">完成支付</text>
						<text class="step-desc">通过江苏银行完成分期支付</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 注意事项 -->
		<view class="notice-card">
			<text class="card-title">注意事项</text>
			<view class="notice-list">
				<text class="notice-item">• 分期购买需要年满18周岁</text>
				<text class="notice-item">• 需要提供有效身份证件</text>
				<text class="notice-item">• 分期额度以银行审批为准</text>
				<text class="notice-item">• 请确保按时还款，避免影响征信</text>
			</view>
		</view>

		<!-- 扫码按钮 -->
		<view class="scan-section">
			<button class="scan-btn" @click="startScan">
				<image class="scan-icon" src="/static/icons/scan.png"></image>
				<text class="scan-text">扫描门店收款码</text>
			</button>
			<text class="scan-tip">请扫描门店提供的收款二维码</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 扫码相关
				scanResult: null,
				isScanning: false
			}
		},
		onLoad() {
			console.log('分期购买页面加载');
		},
		methods: {
			// 开始扫码
			startScan() {
				if (this.isScanning) return;
				
				this.isScanning = true;
				
				// 调用扫码功能
				uni.scanCode({
					success: (res) => {
						console.log('扫码结果:', res);
						this.handleScanResult(res.result);
					},
					fail: (err) => {
						console.error('扫码失败:', err);
						uni.showToast({
							title: '扫码失败，请重试',
							icon: 'none'
						});
						this.isScanning = false;
					}
				});
			},

			// 处理扫码结果
			handleScanResult(result) {
				this.isScanning = false;
				
				try {
					// 解析扫码结果
					const scanData = this.parseScanResult(result);
					
					if (!scanData) {
						uni.showToast({
							title: '无效的二维码',
							icon: 'none'
						});
						return;
					}
					
					// 验证门店信息
					this.validateStoreInfo(scanData);
					
				} catch (error) {
					console.error('解析扫码结果失败:', error);
					uni.showToast({
						title: '二维码格式错误',
						icon: 'none'
					});
				}
			},

			// 解析扫码结果
			parseScanResult(result) {
				try {
					// 尝试解析JSON格式的二维码
					const data = JSON.parse(result);
					
					// 验证必要字段
					if (data.storeId && data.storeName) {
						return {
							storeId: data.storeId,
							storeName: data.storeName,
							storeAddress: data.storeAddress || '',
							storePhone: data.storePhone || '',
							qrCode: data.qrCode || result
						};
					}
					
					return null;
				} catch (e) {
					// 如果不是JSON格式，尝试其他解析方式
					if (result.includes('storeId=')) {
						const params = new URLSearchParams(result.split('?')[1]);
						return {
							storeId: params.get('storeId'),
							storeName: params.get('storeName') || '未知门店',
							storeAddress: params.get('storeAddress') || '',
							storePhone: params.get('storePhone') || '',
							qrCode: result
						};
					}
					
					return null;
				}
			},

			// 验证门店信息
			async validateStoreInfo(storeData) {
				uni.showLoading({
					title: '验证门店信息...'
				});
				
				try {
					// TODO: 调用后端API验证门店信息
					console.log('验证门店信息:', storeData);
					
					// 模拟验证请求
					await this.mockValidateStore(storeData);
					
					uni.hideLoading();
					
					// 验证成功，跳转到商品列表页面
					uni.navigateTo({
						url: `/pages/installment/products?storeId=${storeData.storeId}&storeName=${encodeURIComponent(storeData.storeName)}&storeAddress=${encodeURIComponent(storeData.storeAddress)}&storePhone=${storeData.storePhone}`
					});
					
				} catch (error) {
					uni.hideLoading();
					console.error('验证门店失败:', error);
					uni.showToast({
						title: error.message || '门店验证失败',
						icon: 'none'
					});
				}
			},

			// 模拟验证门店
			mockValidateStore(storeData) {
				return new Promise((resolve, reject) => {
					setTimeout(() => {
						// 模拟验证成功
						if (storeData.storeId) {
							resolve();
						} else {
							reject(new Error('无效的门店信息'));
						}
					}, 1000);
				});
			}
		}
	}
</script>

<style scoped>
	.installment-page {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 0 30rpx 40rpx;
	}

	/* 顶部装饰 */
	.header-section {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 80rpx 0 60rpx;
	}

	.installment-icon {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 30rpx;
	}

	.page-title {
		font-size: 48rpx;
		font-weight: bold;
		color: white;
		margin-bottom: 15rpx;
	}

	.page-subtitle {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.8);
	}

	/* 信息卡片 */
	.info-card {
		background-color: white;
		border-radius: 20rpx;
		padding: 40rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	}

	.card-header {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
		padding-bottom: 20rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.bank-logo {
		width: 80rpx;
		height: 80rpx;
		margin-right: 20rpx;
	}

	.bank-info {
		flex: 1;
	}

	.bank-name {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 8rpx;
	}

	.bank-desc {
		font-size: 24rpx;
		color: #666;
	}

	.installment-details {
		display: flex;
		flex-direction: column;
		gap: 25rpx;
	}

	.detail-item {
		display: flex;
		align-items: center;
	}

	.detail-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 20rpx;
	}

	.detail-content {
		flex: 1;
	}

	.detail-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 5rpx;
	}

	.detail-desc {
		font-size: 24rpx;
		color: #666;
	}

	/* 流程卡片 */
	.process-card {
		background-color: white;
		border-radius: 20rpx;
		padding: 40rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	}

	.card-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 30rpx;
		display: block;
	}

	.process-steps {
		display: flex;
		flex-direction: column;
		gap: 25rpx;
	}

	.step-item {
		display: flex;
		align-items: flex-start;
	}

	.step-number {
		width: 50rpx;
		height: 50rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
		font-size: 24rpx;
		font-weight: bold;
		border-radius: 25rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 20rpx;
		flex-shrink: 0;
	}

	.step-content {
		flex: 1;
		padding-top: 8rpx;
	}

	.step-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 8rpx;
	}

	.step-desc {
		font-size: 24rpx;
		color: #666;
		line-height: 1.4;
	}

	/* 注意事项卡片 */
	.notice-card {
		background-color: white;
		border-radius: 20rpx;
		padding: 40rpx;
		margin-bottom: 40rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	}

	.notice-list {
		display: flex;
		flex-direction: column;
		gap: 15rpx;
	}

	.notice-item {
		font-size: 24rpx;
		color: #666;
		line-height: 1.5;
	}

	/* 扫码区域 */
	.scan-section {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.scan-btn {
		width: 100%;
		height: 100rpx;
		background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
		color: white;
		font-size: 32rpx;
		font-weight: bold;
		border-radius: 20rpx;
		border: none;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 20rpx;
		box-shadow: 0 4rpx 20rpx rgba(255, 107, 107, 0.3);
		transition: all 0.3s ease;
	}

	.scan-btn:active {
		transform: scale(0.98);
	}

	.scan-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 15rpx;
	}

	.scan-text {
		font-size: 32rpx;
	}

	.scan-tip {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
	}
</style>
