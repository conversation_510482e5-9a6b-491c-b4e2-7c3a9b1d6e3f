<template>
	<view class="payment-page">
		<!-- 支付状态 -->
		<view class="payment-status">
			<view class="status-icon" :class="paymentStatus">
				<image v-if="paymentStatus === 'pending'" src="/static/icons/loading.png" class="loading-icon"></image>
				<image v-else-if="paymentStatus === 'success'" src="/static/icons/success.png" class="success-icon"></image>
				<image v-else-if="paymentStatus === 'failed'" src="/static/icons/error.png" class="error-icon"></image>
			</view>
			<text class="status-title">{{statusTitle}}</text>
			<text class="status-desc">{{statusDesc}}</text>
		</view>

		<!-- 订单信息 -->
		<view class="order-info" v-if="orderInfo">
			<view class="info-card">
				<text class="card-title">订单信息</text>
				<view class="info-item">
					<text class="info-label">订单号：</text>
					<text class="info-value">{{orderInfo.orderNo}}</text>
				</view>
				<view class="info-item">
					<text class="info-label">订单金额：</text>
					<text class="info-value amount">¥{{orderInfo.totalAmount}}</text>
				</view>
				<view class="info-item">
					<text class="info-label">分期金额：</text>
					<text class="info-value installment">¥{{orderInfo.installmentAmount}}/期 × 36期</text>
				</view>
				<view class="info-item">
					<text class="info-label">创建时间：</text>
					<text class="info-value">{{orderInfo.createTime}}</text>
				</view>
			</view>
		</view>

		<!-- 支付进度 -->
		<view class="payment-progress" v-if="paymentStatus === 'pending'">
			<view class="progress-steps">
				<view class="step-item" :class="{ 'active': currentStep >= 1 }">
					<view class="step-number">1</view>
					<text class="step-text">订单创建</text>
				</view>
				<view class="step-line" :class="{ 'active': currentStep >= 2 }"></view>
				<view class="step-item" :class="{ 'active': currentStep >= 2 }">
					<view class="step-number">2</view>
					<text class="step-text">跳转支付</text>
				</view>
				<view class="step-line" :class="{ 'active': currentStep >= 3 }"></view>
				<view class="step-item" :class="{ 'active': currentStep >= 3 }">
					<view class="step-number">3</view>
					<text class="step-text">支付完成</text>
				</view>
			</view>
		</view>

		<!-- 支付说明 -->
		<view class="payment-notice" v-if="paymentStatus === 'pending'">
			<text class="notice-title">支付说明</text>
			<view class="notice-content">
				<text class="notice-item">• 系统将自动跳转到江苏银行支付页面</text>
				<text class="notice-item">• 请使用江苏银行APP或网银完成支付</text>
				<text class="notice-item">• 支付完成后将自动返回本页面</text>
				<text class="notice-item">• 如支付遇到问题，请联系客服</text>
			</view>
		</view>

		<!-- 操作按钮 -->
		<view class="action-buttons">
			<button 
				class="action-btn primary" 
				v-if="paymentStatus === 'pending'"
				@click="checkPaymentStatus"
			>
				检查支付状态
			</button>
			
			<button 
				class="action-btn secondary" 
				v-if="paymentStatus === 'success'"
				@click="goToOrders"
			>
				查看订单
			</button>
			
			<button 
				class="action-btn secondary" 
				v-if="paymentStatus === 'failed'"
				@click="retryPayment"
			>
				重新支付
			</button>
			
			<button 
				class="action-btn default" 
				@click="goToHome"
			>
				返回首页
			</button>
		</view>

		<!-- 客服信息 -->
		<view class="customer-service">
			<text class="service-title">需要帮助？</text>
			<view class="service-info">
				<text class="service-item">客服电话：400-123-4567</text>
				<text class="service-item">服务时间：9:00-21:00</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 订单信息
				orderInfo: null,
				// 支付状态：pending-等待支付，success-支付成功，failed-支付失败
				paymentStatus: 'pending',
				// 当前步骤
				currentStep: 1,
				// 检查支付状态的定时器
				checkTimer: null
			}
		},
		computed: {
			// 状态标题
			statusTitle() {
				switch (this.paymentStatus) {
					case 'pending':
						return '等待支付';
					case 'success':
						return '支付成功';
					case 'failed':
						return '支付失败';
					default:
						return '处理中';
				}
			},
			// 状态描述
			statusDesc() {
				switch (this.paymentStatus) {
					case 'pending':
						return '正在跳转到江苏银行支付页面...';
					case 'success':
						return '恭喜！您的订单支付成功，请等待商品配送';
					case 'failed':
						return '支付失败，请重新尝试或联系客服';
					default:
						return '请稍候...';
				}
			}
		},
		onLoad(options) {
			// 获取订单信息
			this.orderInfo = {
				orderId: options.orderId || '',
				orderNo: 'ORDER' + Math.random().toString(36).substr(2, 9).toUpperCase(),
				totalAmount: parseFloat(options.totalAmount) || 0,
				installmentAmount: (parseFloat(options.totalAmount) / 36).toFixed(2),
				createTime: this.formatTime(new Date())
			};
			
			this.startPaymentProcess();
		},
		onUnload() {
			// 页面卸载时清除定时器
			if (this.checkTimer) {
				clearInterval(this.checkTimer);
			}
		},
		methods: {
			// 开始支付流程
			async startPaymentProcess() {
				try {
					// 步骤1：订单创建完成
					this.currentStep = 1;
					await this.delay(1000);
					
					// 步骤2：跳转支付
					this.currentStep = 2;
					await this.delay(1000);
					
					// 跳转到江苏银行支付页面
					await this.redirectToPayment();
					
					// 步骤3：开始检查支付状态
					this.currentStep = 3;
					this.startCheckPaymentStatus();
					
				} catch (error) {
					console.error('支付流程失败:', error);
					this.paymentStatus = 'failed';
				}
			},

			// 跳转到支付页面
			async redirectToPayment() {
				// 模拟跳转到江苏银行支付页面
				uni.showModal({
					title: '跳转支付',
					content: '即将跳转到江苏银行支付页面，请完成支付后返回',
					showCancel: false,
					success: () => {
						// 模拟支付页面跳转
						// 在实际项目中，这里应该跳转到真实的支付页面
						console.log('跳转到江苏银行支付页面');
						
						// 模拟支付完成（实际项目中由支付回调处理）
						setTimeout(() => {
							this.simulatePaymentComplete();
						}, 5000);
					}
				});
			},

			// 模拟支付完成
			simulatePaymentComplete() {
				// 模拟支付成功
				this.paymentStatus = 'success';
				this.currentStep = 3;
				
				// 清除检查定时器
				if (this.checkTimer) {
					clearInterval(this.checkTimer);
				}
				
				// 更新订单状态
				this.updateOrderStatus('paid');
			},

			// 开始检查支付状态
			startCheckPaymentStatus() {
				// 每5秒检查一次支付状态
				this.checkTimer = setInterval(() => {
					this.checkPaymentStatus();
				}, 5000);
			},

			// 检查支付状态
			async checkPaymentStatus() {
				try {
					// TODO: 调用后端API检查支付状态
					console.log('检查支付状态，订单ID:', this.orderInfo.orderId);
					
					// 模拟检查结果
					const status = await this.mockCheckPaymentStatus();
					
					if (status === 'success') {
						this.paymentStatus = 'success';
						this.currentStep = 3;
						
						// 清除定时器
						if (this.checkTimer) {
							clearInterval(this.checkTimer);
						}
						
						// 更新订单状态
						this.updateOrderStatus('paid');
						
					} else if (status === 'failed') {
						this.paymentStatus = 'failed';
						
						// 清除定时器
						if (this.checkTimer) {
							clearInterval(this.checkTimer);
						}
					}
					
				} catch (error) {
					console.error('检查支付状态失败:', error);
				}
			},

			// 模拟检查支付状态
			mockCheckPaymentStatus() {
				return new Promise((resolve) => {
					// 模拟随机结果
					const random = Math.random();
					if (random > 0.7) {
						resolve('success');
					} else if (random > 0.9) {
						resolve('failed');
					} else {
						resolve('pending');
					}
				});
			},

			// 更新订单状态
			async updateOrderStatus(status) {
				try {
					// TODO: 调用后端API更新订单状态
					console.log('更新订单状态:', status);
					
					// 模拟更新成功
					uni.showToast({
						title: '订单状态已更新',
						icon: 'success'
					});
					
				} catch (error) {
					console.error('更新订单状态失败:', error);
				}
			},

			// 重新支付
			retryPayment() {
				this.paymentStatus = 'pending';
				this.currentStep = 1;
				this.startPaymentProcess();
			},

			// 查看订单
			goToOrders() {
				uni.switchTab({
					url: '/pages/mine/mine'
				});
			},

			// 返回首页
			goToHome() {
				uni.switchTab({
					url: '/pages/index/index'
				});
			},

			// 延迟函数
			delay(ms) {
				return new Promise(resolve => setTimeout(resolve, ms));
			},

			// 格式化时间
			formatTime(date) {
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				const seconds = String(date.getSeconds()).padStart(2, '0');
				
				return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
			}
		}
	}
</script>

<style scoped>
	.payment-page {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 40rpx 30rpx;
	}

	/* 支付状态 */
	.payment-status {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 60rpx 0;
	}

	.status-icon {
		width: 120rpx;
		height: 120rpx;
		border-radius: 60rpx;
		background-color: rgba(255, 255, 255, 0.2);
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 30rpx;
	}

	.status-icon.pending {
		background-color: rgba(255, 193, 7, 0.2);
	}

	.status-icon.success {
		background-color: rgba(40, 167, 69, 0.2);
	}

	.status-icon.failed {
		background-color: rgba(220, 53, 69, 0.2);
	}

	.loading-icon {
		width: 60rpx;
		height: 60rpx;
		animation: rotate 2s linear infinite;
	}

	.success-icon, .error-icon {
		width: 60rpx;
		height: 60rpx;
	}

	@keyframes rotate {
		from { transform: rotate(0deg); }
		to { transform: rotate(360deg); }
	}

	.status-title {
		font-size: 36rpx;
		font-weight: bold;
		color: white;
		margin-bottom: 15rpx;
	}

	.status-desc {
		font-size: 26rpx;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		line-height: 1.5;
	}

	/* 订单信息 */
	.order-info {
		margin-bottom: 30rpx;
	}

	.info-card {
		background-color: white;
		border-radius: 20rpx;
		padding: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	}

	.card-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
		display: block;
	}

	.info-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15rpx;
	}

	.info-item:last-child {
		margin-bottom: 0;
	}

	.info-label {
		font-size: 26rpx;
		color: #666;
	}

	.info-value {
		font-size: 26rpx;
		color: #333;
	}

	.info-value.amount {
		font-size: 32rpx;
		font-weight: bold;
		color: #ff6b6b;
	}

	.info-value.installment {
		color: #667eea;
		font-weight: bold;
	}

	/* 支付进度 */
	.payment-progress {
		background-color: white;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	}

	.progress-steps {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.step-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
	}

	.step-number {
		width: 50rpx;
		height: 50rpx;
		border-radius: 25rpx;
		background-color: #e0e0e0;
		color: #999;
		font-size: 24rpx;
		font-weight: bold;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 10rpx;
		transition: all 0.3s ease;
	}

	.step-item.active .step-number {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
	}

	.step-text {
		font-size: 22rpx;
		color: #666;
		text-align: center;
	}

	.step-item.active .step-text {
		color: #333;
		font-weight: bold;
	}

	.step-line {
		width: 80rpx;
		height: 2rpx;
		background-color: #e0e0e0;
		margin: 0 20rpx;
		transition: all 0.3s ease;
	}

	.step-line.active {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	}

	/* 支付说明 */
	.payment-notice {
		background-color: white;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	}

	.notice-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
		display: block;
	}

	.notice-content {
		display: flex;
		flex-direction: column;
		gap: 10rpx;
	}

	.notice-item {
		font-size: 24rpx;
		color: #666;
		line-height: 1.5;
	}

	/* 操作按钮 */
	.action-buttons {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
		margin-bottom: 30rpx;
	}

	.action-btn {
		height: 80rpx;
		border-radius: 15rpx;
		font-size: 28rpx;
		font-weight: bold;
		border: none;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
	}

	.action-btn:active {
		transform: scale(0.98);
	}

	.action-btn.primary {
		background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
		color: white;
	}

	.action-btn.secondary {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
	}

	.action-btn.default {
		background-color: rgba(255, 255, 255, 0.2);
		color: white;
		border: 2rpx solid rgba(255, 255, 255, 0.3);
	}

	/* 客服信息 */
	.customer-service {
		background-color: rgba(255, 255, 255, 0.1);
		border-radius: 15rpx;
		padding: 20rpx;
		text-align: center;
	}

	.service-title {
		font-size: 26rpx;
		color: white;
		margin-bottom: 10rpx;
		display: block;
	}

	.service-info {
		display: flex;
		flex-direction: column;
		gap: 5rpx;
	}

	.service-item {
		font-size: 22rpx;
		color: rgba(255, 255, 255, 0.8);
	}
</style>
