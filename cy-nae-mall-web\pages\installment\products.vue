<template>
	<view class="products-page">
		<!-- 门店信息 -->
		<view class="store-info">
			<view class="store-header">
				<image class="store-icon" src="/static/icons/store.png"></image>
				<view class="store-details">
					<text class="store-name">{{storeInfo.name}}</text>
					<text class="store-address">{{storeInfo.address}}</text>
					<text class="store-phone" v-if="storeInfo.phone">电话：{{storeInfo.phone}}</text>
				</view>
			</view>
		</view>

		<!-- 商品列表 -->
		<view class="products-container">
			<view class="products-list">
				<view 
					class="product-item" 
					v-for="(product, index) in products" 
					:key="product.id"
					@click="selectProduct(product)"
				>
					<view class="product-image-wrapper">
						<image class="product-image" :src="product.images[0]" mode="aspectFill"></image>
						<view class="product-badge" v-if="product.is_hot">热销</view>
					</view>
					
					<view class="product-info">
						<text class="product-name">{{product.name}}</text>
						<text class="product-desc">{{product.description}}</text>
						
						<view class="product-price-section">
							<view class="price-info">
								<text class="current-price">¥{{product.price}}</text>
								<text class="original-price" v-if="product.original_price > product.price">¥{{product.original_price}}</text>
							</view>
							<view class="installment-info">
								<text class="installment-price">¥{{(product.price / 36).toFixed(2)}}/期</text>
								<text class="installment-period">36期免息</text>
							</view>
						</view>
						
						<view class="product-actions">
							<view class="quantity-control">
								<button 
									class="quantity-btn" 
									:class="{ 'quantity-btn-disabled': product.quantity <= 0 }"
									@click.stop="decreaseQuantity(product)"
								>-</button>
								<text class="quantity-text">{{product.quantity}}</text>
								<button 
									class="quantity-btn" 
									:class="{ 'quantity-btn-disabled': product.quantity >= product.stock }"
									@click.stop="increaseQuantity(product)"
								>+</button>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部浮动栏 -->
		<view class="bottom-bar" v-if="selectedProducts.length > 0">
			<view class="total-info">
				<view class="total-amount">
					<text class="total-label">总价：</text>
					<text class="total-price">¥{{totalAmount}}</text>
				</view>
				<view class="installment-amount">
					<text class="installment-label">每期：</text>
					<text class="installment-price">¥{{(totalAmount / 36).toFixed(2)}}</text>
				</view>
			</view>
			<button 
				class="purchase-btn" 
				:class="{ 'purchase-btn-disabled': !canPurchase }"
				:disabled="!canPurchase"
				@click="confirmPurchase"
			>
				{{isCreatingOrder ? '创建订单中...' : '确定购买'}}
			</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 门店信息
				storeInfo: {
					id: '',
					name: '',
					address: '',
					phone: ''
				},
				// 商品列表
				products: [],
				// 选中的商品
				selectedProducts: [],
				// 是否正在创建订单
				isCreatingOrder: false
			}
		},
		computed: {
			// 总金额
			totalAmount() {
				return this.selectedProducts.reduce((total, product) => {
					return total + (product.price * product.quantity);
				}, 0);
			},
			// 是否可以购买
			canPurchase() {
				return this.selectedProducts.length > 0 && 
					   this.selectedProducts.some(p => p.quantity > 0) && 
					   !this.isCreatingOrder;
			}
		},
		onLoad(options) {
			// 获取门店信息
			this.storeInfo = {
				id: options.storeId || '',
				name: decodeURIComponent(options.storeName || ''),
				address: decodeURIComponent(options.storeAddress || ''),
				phone: options.storePhone || ''
			};
			
			this.loadProducts();
		},
		methods: {
			// 加载商品列表
			async loadProducts() {
				uni.showLoading({
					title: '加载商品中...'
				});
				
				try {
					// TODO: 调用后端API获取商品列表
					console.log('加载商品列表，门店ID:', this.storeInfo.id);
					
					// 模拟商品数据
					this.products = [
						{
							id: 1,
							name: '智能手机 Pro Max',
							description: '最新款智能手机，性能强劲，拍照出色',
							price: 5999.00,
							original_price: 6999.00,
							stock: 50,
							images: ['/static/products/phone1.jpg'],
							is_hot: true,
							quantity: 0
						},
						{
							id: 2,
							name: '无线蓝牙耳机',
							description: '高品质无线蓝牙耳机，降噪效果出色',
							price: 899.00,
							original_price: 1299.00,
							stock: 100,
							images: ['/static/products/earphone1.jpg'],
							is_hot: true,
							quantity: 0
						},
						{
							id: 3,
							name: '智能手表',
							description: '健康监测智能手表，续航持久',
							price: 1999.00,
							original_price: 2499.00,
							stock: 30,
							images: ['/static/products/watch1.jpg'],
							is_hot: false,
							quantity: 0
						},
						{
							id: 4,
							name: '平板电脑',
							description: '大屏平板电脑，办公娱乐两不误',
							price: 2999.00,
							original_price: 3499.00,
							stock: 20,
							images: ['/static/products/tablet1.jpg'],
							is_hot: false,
							quantity: 0
						}
					];
					
					uni.hideLoading();
					
				} catch (error) {
					uni.hideLoading();
					console.error('加载商品失败:', error);
					uni.showToast({
						title: '加载商品失败',
						icon: 'none'
					});
				}
			},

			// 选择商品
			selectProduct(product) {
				// 如果商品未选中，添加到选中列表
				if (!this.selectedProducts.find(p => p.id === product.id)) {
					this.selectedProducts.push({...product});
				}
			},

			// 增加数量
			increaseQuantity(product) {
				if (product.quantity >= product.stock) return;
				
				product.quantity++;
				this.updateSelectedProduct(product);
			},

			// 减少数量
			decreaseQuantity(product) {
				if (product.quantity <= 0) return;
				
				product.quantity--;
				this.updateSelectedProduct(product);
			},

			// 更新选中商品
			updateSelectedProduct(product) {
				const index = this.selectedProducts.findIndex(p => p.id === product.id);
				if (index !== -1) {
					if (product.quantity > 0) {
						this.selectedProducts[index] = {...product};
					} else {
						this.selectedProducts.splice(index, 1);
					}
				}
			},

			// 确认购买
			async confirmPurchase() {
				if (!this.canPurchase) return;
				
				// 检查是否登录
				const token = uni.getStorageSync('token');
				if (!token) {
					uni.showModal({
						title: '提示',
						content: '请先登录后再购买',
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/login/login'
								});
							}
						}
					});
					return;
				}
				
				this.isCreatingOrder = true;
				
				try {
					// 创建订单
					const orderData = {
						storeId: this.storeInfo.id,
						storeName: this.storeInfo.name,
						products: this.selectedProducts.filter(p => p.quantity > 0),
						totalAmount: this.totalAmount,
						installmentAmount: (this.totalAmount / 36).toFixed(2),
						installmentPeriods: 36
					};
					
					console.log('创建订单:', orderData);
					
					// TODO: 调用后端API创建订单
					const orderResult = await this.mockCreateOrder(orderData);
					
					// 跳转到支付页面
					uni.navigateTo({
						url: `/pages/installment/payment?orderId=${orderResult.orderId}&totalAmount=${this.totalAmount}`
					});
					
				} catch (error) {
					console.error('创建订单失败:', error);
					uni.showToast({
						title: error.message || '创建订单失败',
						icon: 'none'
					});
				} finally {
					this.isCreatingOrder = false;
				}
			},

			// 模拟创建订单
			mockCreateOrder(orderData) {
				return new Promise((resolve, reject) => {
					setTimeout(() => {
						// 模拟创建订单成功
						resolve({
							orderId: 'ORD' + Date.now(),
							orderNo: 'ORDER' + Math.random().toString(36).substr(2, 9).toUpperCase(),
							status: 'pending'
						});
					}, 1000);
				});
			}
		}
	}
</script>

<style scoped>
	.products-page {
		min-height: 100vh;
		background-color: #f8f8f8;
		padding-bottom: 120rpx;
	}

	/* 门店信息 */
	.store-info {
		background-color: white;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.store-header {
		display: flex;
		align-items: center;
	}

	.store-icon {
		width: 60rpx;
		height: 60rpx;
		margin-right: 20rpx;
	}

	.store-details {
		flex: 1;
	}

	.store-name {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 8rpx;
	}

	.store-address {
		font-size: 26rpx;
		color: #666;
		display: block;
		margin-bottom: 5rpx;
	}

	.store-phone {
		font-size: 24rpx;
		color: #999;
	}

	/* 商品列表 */
	.products-container {
		padding: 0 20rpx;
	}

	.products-list {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.product-item {
		background-color: white;
		border-radius: 15rpx;
		padding: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
		display: flex;
		gap: 20rpx;
	}

	.product-image-wrapper {
		position: relative;
		width: 200rpx;
		height: 200rpx;
		flex-shrink: 0;
	}

	.product-image {
		width: 100%;
		height: 100%;
		border-radius: 10rpx;
	}

	.product-badge {
		position: absolute;
		top: 10rpx;
		left: 10rpx;
		background-color: #ff4757;
		color: white;
		font-size: 20rpx;
		padding: 4rpx 8rpx;
		border-radius: 10rpx;
	}

	.product-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.product-name {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
		line-height: 1.4;
		margin-bottom: 10rpx;
	}

	.product-desc {
		font-size: 24rpx;
		color: #666;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
		line-height: 1.4;
		margin-bottom: 15rpx;
	}

	.product-price-section {
		margin-bottom: 15rpx;
	}

	.price-info {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
	}

	.current-price {
		font-size: 32rpx;
		font-weight: bold;
		color: #ff6b6b;
		margin-right: 15rpx;
	}

	.original-price {
		font-size: 24rpx;
		color: #999;
		text-decoration: line-through;
	}

	.installment-info {
		display: flex;
		align-items: center;
		gap: 15rpx;
	}

	.installment-price {
		font-size: 26rpx;
		font-weight: bold;
		color: #667eea;
	}

	.installment-period {
		font-size: 22rpx;
		color: #999;
		background-color: #f0f0f0;
		padding: 4rpx 8rpx;
		border-radius: 8rpx;
	}

	.product-actions {
		display: flex;
		justify-content: flex-end;
	}

	.quantity-control {
		display: flex;
		align-items: center;
		border: 1rpx solid #e0e0e0;
		border-radius: 8rpx;
		overflow: hidden;
	}

	.quantity-btn {
		width: 60rpx;
		height: 60rpx;
		background-color: #f8f9fa;
		border: none;
		font-size: 28rpx;
		color: #333;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.quantity-btn-disabled {
		background-color: #f0f0f0;
		color: #ccc;
	}

	.quantity-text {
		width: 80rpx;
		height: 60rpx;
		text-align: center;
		line-height: 60rpx;
		font-size: 28rpx;
		color: #333;
		background-color: white;
		border-left: 1rpx solid #e0e0e0;
		border-right: 1rpx solid #e0e0e0;
	}

	/* 底部浮动栏 */
	.bottom-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: white;
		padding: 20rpx 30rpx;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
		display: flex;
		align-items: center;
		gap: 20rpx;
		z-index: 1000;
	}

	.total-info {
		flex: 1;
	}

	.total-amount {
		display: flex;
		align-items: center;
		margin-bottom: 5rpx;
	}

	.total-label {
		font-size: 26rpx;
		color: #666;
		margin-right: 10rpx;
	}

	.total-price {
		font-size: 32rpx;
		font-weight: bold;
		color: #ff6b6b;
	}

	.installment-amount {
		display: flex;
		align-items: center;
	}

	.installment-label {
		font-size: 22rpx;
		color: #999;
		margin-right: 10rpx;
	}

	.installment-price {
		font-size: 24rpx;
		color: #667eea;
		font-weight: bold;
	}

	.purchase-btn {
		width: 200rpx;
		height: 80rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
		font-size: 28rpx;
		font-weight: bold;
		border-radius: 15rpx;
		border: none;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
	}

	.purchase-btn:active {
		transform: scale(0.98);
	}

	.purchase-btn-disabled {
		background: #ccc;
		color: #999;
	}
</style>
