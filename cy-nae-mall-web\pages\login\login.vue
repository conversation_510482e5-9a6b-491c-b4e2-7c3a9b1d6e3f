<template>
  <view class="login-page">
    <!-- 背景装饰 -->
    <view class="background-decoration">
      <view class="decoration-circle circle-1"></view>
      <view class="decoration-circle circle-2"></view>
      <view class="decoration-circle circle-3"></view>
    </view>

    <!-- 顶部区域 -->
    <view class="header-section">
      <view class="logo-container">
        <image class="logo" src="/static/logo.png" mode="aspectFit"></image>
        <view class="logo-glow"></view>
      </view>
      <text class="app-name">传艺商城</text>
      <text class="welcome-text">世界品牌，中国传艺</text>
      <text class="subtitle">欢迎回来，请登录您的账户</text>
    </view>

    <!-- 登录表单卡片 -->
    <view class="form-card">
      <view class="card-header">
        <text class="form-title">账户登录</text>
        <view class="title-underline"></view>
      </view>

      <view class="form-content">
        <view class="form-group">
          <view class="input-container">
            <view
              class="input-wrapper"
              :class="{
                'input-focused': focusedInput === 'username',
                'input-error': errors.username,
              }"
            >
              <view class="input-icon-wrapper">
                <text class="input-icon">👤</text>
              </view>
              <input
                class="form-input"
                type="text"
                placeholder="手机号或用户名"
                v-model="loginForm.username"
                @focus="focusedInput = 'username'"
                @blur="
                  focusedInput = '';
                  validateUsername();
                "
              />
            </view>
            <view class="error-message" v-if="errors.username">
              <text class="error-text">{{ errors.username }}</text>
            </view>
          </view>
        </view>

        <view class="form-group">
          <view class="input-container">
            <view
              class="input-wrapper"
              :class="{
                'input-focused': focusedInput === 'password',
                'input-error': errors.password,
              }"
            >
              <view class="input-icon-wrapper">
                <text class="input-icon">🔒</text>
              </view>
              <input
                class="form-input"
                :type="showPassword ? 'text' : 'password'"
                placeholder="请输入密码"
                v-model="loginForm.password"
                @focus="focusedInput = 'password'"
                @blur="
                  focusedInput = '';
                  validatePassword();
                "
              />
              <view class="password-toggle" @click="togglePassword">
                <text class="toggle-icon">{{
                  showPassword ? "👁️" : "👁️‍🗨️"
                }}</text>
              </view>
            </view>
            <view class="error-message" v-if="errors.password">
              <text class="error-text">{{ errors.password }}</text>
            </view>
          </view>
        </view>

        <view class="form-options">
          <view class="remember-section" @click="toggleRemember">
            <view
              class="custom-checkbox"
              :class="{ 'checkbox-checked': rememberMe }"
            >
              <text class="checkbox-icon" v-if="rememberMe">✓</text>
            </view>
            <text class="checkbox-label">记住密码</text>
          </view>
          <text class="forgot-link" @click="forgotPassword">忘记密码？</text>
        </view>

        <button
          class="login-button"
          :class="{ 'button-disabled': !canLogin, 'button-loading': isLogging }"
          :disabled="!canLogin"
          @click="handleLogin"
        >
          <view class="button-content">
            <view class="loading-spinner" v-if="isLogging"></view>
            <text class="button-text">{{
              isLogging ? "登录中..." : "立即登录"
            }}</text>
          </view>
        </button>

        <view class="register-section">
          <text class="register-text">还没有账号？</text>
          <text class="register-link" @click="goToRegister">立即注册</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 登录表单数据
      loginForm: {
        username: "",
        password: "",
      },
      // 表单验证错误
      errors: {
        username: "",
        password: "",
      },
      // 是否显示密码
      showPassword: false,
      // 是否记住密码
      rememberMe: false,
      // 是否正在登录
      isLogging: false,
      // 当前聚焦的输入框
      focusedInput: "",
    };
  },
  computed: {
    // 是否可以登录
    canLogin() {
      return (
        this.loginForm.username.trim() &&
        this.loginForm.password.trim() &&
        !this.errors.username &&
        !this.errors.password &&
        !this.isLogging
      );
    },
  },
  onLoad() {
    this.loadRememberedData();
  },
  methods: {
    // 加载记住的登录信息
    loadRememberedData() {
      const remembered = uni.getStorageSync("rememberedLogin");
      if (remembered) {
        this.loginForm.username = remembered.username || "";
        this.loginForm.password = remembered.password || "";
        this.rememberMe = true;
      }
    },

    // 验证用户名
    validateUsername() {
      const username = this.loginForm.username.trim();
      if (!username) {
        this.errors.username = "请输入手机号或用户名";
        return false;
      }

      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/;
      // 验证用户名格式（字母数字下划线，3-20位）
      const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;

      if (!phoneRegex.test(username) && !usernameRegex.test(username)) {
        this.errors.username = "请输入正确的手机号或用户名";
        return false;
      }

      this.errors.username = "";
      return true;
    },

    // 验证密码
    validatePassword() {
      const password = this.loginForm.password.trim();
      if (!password) {
        this.errors.password = "请输入密码";
        return false;
      }

      if (password.length < 6) {
        this.errors.password = "密码至少6位";
        return false;
      }

      this.errors.password = "";
      return true;
    },

    // 切换密码显示
    togglePassword() {
      this.showPassword = !this.showPassword;
    },

    // 切换记住密码
    toggleRemember() {
      this.rememberMe = !this.rememberMe;
    },

    // 忘记密码
    forgotPassword() {
      uni.showModal({
        title: "忘记密码",
        content: "请联系客服重置密码\n客服电话：************",
        showCancel: false,
        confirmText: "知道了",
      });
    },

    // 处理登录
    async handleLogin() {
      if (!this.validateUsername() || !this.validatePassword()) {
        return;
      }

      this.isLogging = true;

      try {
        // TODO: 调用登录API
        console.log("登录请求:", this.loginForm);

        // 模拟登录请求
        await this.mockLogin();

        // 保存登录信息
        if (this.rememberMe) {
          uni.setStorageSync("rememberedLogin", {
            username: this.loginForm.username,
            password: this.loginForm.password,
          });
        } else {
          uni.removeStorageSync("rememberedLogin");
        }

        // 保存用户信息和token
        const userInfo = {
          id: 1,
          username: this.loginForm.username,
          nickname: "用户" + Math.floor(Math.random() * 1000),
          phone: this.loginForm.username,
          avatar: "",
          email: "",
        };

        uni.setStorageSync("token", "mock_token_" + Date.now());
        uni.setStorageSync("userInfo", userInfo);

        uni.showToast({
          title: "登录成功",
          icon: "success",
        });

        // 延迟跳转，让用户看到成功提示
        setTimeout(() => {
          uni.switchTab({
            url: "/pages/index/index",
          });
        }, 1500);
      } catch (error) {
        console.error("登录失败:", error);
        uni.showToast({
          title: error.message || "登录失败",
          icon: "none",
        });
      } finally {
        this.isLogging = false;
      }
    },

    // 模拟登录请求
    mockLogin() {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          // 模拟登录成功
          if (
            this.loginForm.username === "admin" &&
            this.loginForm.password === "123456"
          ) {
            resolve();
          } else {
            reject(new Error("用户名或密码错误"));
          }
        }, 1000);
      });
    },

    // 跳转到注册页面
    goToRegister() {
      uni.navigateTo({
        url: "/pages/register/register",
      });
    },
  },
};
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: -50rpx;
  animation-delay: 0s;
}

.circle-2 {
  width: 150rpx;
  height: 150rpx;
  top: 60%;
  left: -30rpx;
  animation-delay: 2s;
}

.circle-3 {
  width: 100rpx;
  height: 100rpx;
  top: 30%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 顶部区域 */
.header-section {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 40rpx 60rpx;
}

.logo-container {
  position: relative;
  margin-bottom: 40rpx;
}

.logo {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
}

.logo-glow {
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  right: -10rpx;
  bottom: -10rpx;
  border-radius: 50%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.3) 0%,
    transparent 70%
  );
  animation: pulse 3s ease-in-out infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

.app-name {
  font-size: 52rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 12rpx;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
}

.welcome-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

/* 表单卡片 */
.form-card {
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 40rpx 40rpx 40rpx 40rpx;
  margin: 0 20rpx;
  box-shadow: 0 -10rpx 40rpx rgba(0, 0, 0, 0.1);
  max-height: 70vh;
  overflow: hidden;
}

.card-header {
  padding: 30rpx 40rpx 20rpx;
  text-align: center;
}

.form-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.title-underline {
  width: 50rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 2rpx;
  margin: 0 auto;
}

.form-content {
  padding: 0 40rpx 30rpx;
}

.form-group {
  margin-bottom: 20rpx;
}

.input-container {
  position: relative;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.input-wrapper.input-focused {
  border-color: #667eea;
  background: white;
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.12);
  transform: translateY(-1rpx);
}

.input-wrapper.input-error {
  border-color: #ff6b6b;
  background: rgba(255, 107, 107, 0.05);
}

.input-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70rpx;
  height: 80rpx;
}

.input-icon {
  font-size: 28rpx;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.input-focused .input-icon {
  opacity: 1;
}

.form-input {
  flex: 1;
  height: 80rpx;
  font-size: 26rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
}

.form-input::placeholder {
  color: #999;
  font-size: 24rpx;
}

.password-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70rpx;
  height: 80rpx;
  cursor: pointer;
}

.toggle-icon {
  font-size: 28rpx;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.password-toggle:active .toggle-icon {
  opacity: 1;
}

.error-message {
  margin-top: 8rpx;
  margin-left: 16rpx;
}

.error-text {
  font-size: 20rpx;
  color: #ff6b6b;
  display: flex;
  align-items: center;
}

.error-text::before {
  content: "⚠️";
  margin-right: 6rpx;
  font-size: 16rpx;
}

/* 表单选项 */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
  padding: 0 8rpx;
}

.remember-section {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.custom-checkbox {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #ddd;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10rpx;
  transition: all 0.3s ease;
  background: white;
}

.custom-checkbox.checkbox-checked {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.checkbox-icon {
  color: white;
  font-size: 18rpx;
  font-weight: bold;
}

.checkbox-label {
  font-size: 24rpx;
  color: #666;
  user-select: none;
}

.forgot-link {
  font-size: 24rpx;
  color: #667eea;
  text-decoration: none;
  position: relative;
}

.forgot-link::after {
  content: "";
  position: absolute;
  bottom: -2rpx;
  left: 0;
  width: 0;
  height: 2rpx;
  background: #667eea;
  transition: width 0.3s ease;
}

.forgot-link:active::after {
  width: 100%;
}

/* 登录按钮 */
.login-button {
  width: 100%;
  height: 85rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 20rpx;
  margin-bottom: 25rpx;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.25);
}

.login-button:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.4);
}

.login-button.button-disabled {
  background: linear-gradient(135deg, #ccc 0%, #999 100%);
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.login-button.button-loading {
  pointer-events: none;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 12rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.button-text {
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

/* 分割线 */
.divider-section {
  display: flex;
  align-items: center;
  margin: 25rpx 0;
}

.divider-line {
  flex: 1;
  height: 1rpx;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #e0e0e0 50%,
    transparent 100%
  );
}

.divider-text {
  font-size: 22rpx;
  color: #999;
  margin: 0 16rpx;
  background: rgba(255, 255, 255, 0.9);
  padding: 0 8rpx;
}

/* 社交登录 */
.social-login {
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;
}

.social-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 20rpx;
  background: rgba(248, 249, 250, 0.8);
  transition: all 0.3s ease;
  min-width: 120rpx;
}

.social-item:active {
  background: rgba(102, 126, 234, 0.1);
  transform: scale(0.95);
}

.social-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
}

.social-text {
  font-size: 22rpx;
  color: #666;
}

/* 注册区域 */
.register-section {
  text-align: center;
  padding: 15rpx 0;
}

.register-text {
  font-size: 24rpx;
  color: #666;
}

.register-link {
  font-size: 24rpx;
  color: #667eea;
  margin-left: 8rpx;
  font-weight: 600;
  position: relative;
}

.register-link::after {
  content: "";
  position: absolute;
  bottom: -2rpx;
  left: 0;
  width: 0;
  height: 2rpx;
  background: #667eea;
  transition: width 0.3s ease;
}

.register-link:active::after {
  width: 100%;
}
</style>
