<template>
  <view class="login-page">
    <!-- 顶部装饰 -->
    <view class="header-decoration">
      <image class="logo" src="/static/logo.png" mode="aspectFit"></image>
      <text class="app-name">CY商城</text>
      <text class="welcome-text">欢迎登录</text>
    </view>

    <!-- 登录表单 -->
    <view class="login-form">
      <view class="form-group">
        <view class="input-wrapper">
          <image class="input-icon" src="/static/icons/phone.png"></image>
          <input
            class="form-input"
            type="text"
            placeholder="请输入手机号或用户名"
            v-model="loginForm.username"
            @blur="validateUsername"
          />
        </view>
        <text class="error-text" v-if="errors.username">{{
          errors.username
        }}</text>
      </view>

      <view class="form-group">
        <view class="input-wrapper">
          <image class="input-icon" src="/static/icons/password.png"></image>
          <input
            class="form-input"
            :type="showPassword ? 'text' : 'password'"
            placeholder="请输入密码"
            v-model="loginForm.password"
            @blur="validatePassword"
          />
          <image
            class="password-toggle"
            :src="
              showPassword
                ? '/static/icons/eye-open.png'
                : '/static/icons/eye-close.png'
            "
            @click="togglePassword"
          ></image>
        </view>
        <text class="error-text" v-if="errors.password">{{
          errors.password
        }}</text>
      </view>

      <view class="form-options">
        <view class="remember-me" @click="toggleRemember">
          <image
            class="checkbox"
            :src="
              rememberMe
                ? '/static/icons/checkbox-checked.png'
                : '/static/icons/checkbox-unchecked.png'
            "
          ></image>
          <text class="checkbox-text">记住密码</text>
        </view>
        <text class="forgot-password" @click="forgotPassword">忘记密码？</text>
      </view>

      <button
        class="login-btn"
        :class="{ 'login-btn-disabled': !canLogin }"
        :disabled="!canLogin"
        @click="handleLogin"
      >
        {{ isLogging ? "登录中..." : "登录" }}
      </button>

      <view class="divider">
        <view class="divider-line"></view>
        <text class="divider-text">或</text>
        <view class="divider-line"></view>
      </view>

      <view class="register-tip">
        <text class="tip-text">还没有账号？</text>
        <text class="register-link" @click="goToRegister">立即注册</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 登录表单数据
      loginForm: {
        username: "",
        password: "",
      },
      // 表单验证错误
      errors: {
        username: "",
        password: "",
      },
      // 是否显示密码
      showPassword: false,
      // 是否记住密码
      rememberMe: false,
      // 是否正在登录
      isLogging: false,
    };
  },
  computed: {
    // 是否可以登录
    canLogin() {
      return (
        this.loginForm.username.trim() &&
        this.loginForm.password.trim() &&
        !this.errors.username &&
        !this.errors.password &&
        !this.isLogging
      );
    },
  },
  onLoad() {
    this.loadRememberedData();
  },
  methods: {
    // 加载记住的登录信息
    loadRememberedData() {
      const remembered = uni.getStorageSync("rememberedLogin");
      if (remembered) {
        this.loginForm.username = remembered.username || "";
        this.loginForm.password = remembered.password || "";
        this.rememberMe = true;
      }
    },

    // 验证用户名
    validateUsername() {
      const username = this.loginForm.username.trim();
      if (!username) {
        this.errors.username = "请输入手机号或用户名";
        return false;
      }

      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/;
      // 验证用户名格式（字母数字下划线，3-20位）
      const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;

      if (!phoneRegex.test(username) && !usernameRegex.test(username)) {
        this.errors.username = "请输入正确的手机号或用户名";
        return false;
      }

      this.errors.username = "";
      return true;
    },

    // 验证密码
    validatePassword() {
      const password = this.loginForm.password.trim();
      if (!password) {
        this.errors.password = "请输入密码";
        return false;
      }

      if (password.length < 6) {
        this.errors.password = "密码至少6位";
        return false;
      }

      this.errors.password = "";
      return true;
    },

    // 切换密码显示
    togglePassword() {
      this.showPassword = !this.showPassword;
    },

    // 切换记住密码
    toggleRemember() {
      this.rememberMe = !this.rememberMe;
    },

    // 忘记密码
    forgotPassword() {
      uni.showModal({
        title: "忘记密码",
        content: "请联系客服重置密码\n客服电话：************",
        showCancel: false,
        confirmText: "知道了",
      });
    },

    // 处理登录
    async handleLogin() {
      if (!this.validateUsername() || !this.validatePassword()) {
        return;
      }

      this.isLogging = true;

      try {
        // TODO: 调用登录API
        console.log("登录请求:", this.loginForm);

        // 模拟登录请求
        await this.mockLogin();

        // 保存登录信息
        if (this.rememberMe) {
          uni.setStorageSync("rememberedLogin", {
            username: this.loginForm.username,
            password: this.loginForm.password,
          });
        } else {
          uni.removeStorageSync("rememberedLogin");
        }

        // 保存用户信息和token
        const userInfo = {
          id: 1,
          username: this.loginForm.username,
          nickname: "用户" + Math.floor(Math.random() * 1000),
          phone: this.loginForm.username,
          avatar: "",
          email: "",
        };

        uni.setStorageSync("token", "mock_token_" + Date.now());
        uni.setStorageSync("userInfo", userInfo);

        uni.showToast({
          title: "登录成功",
          icon: "success",
        });

        // 延迟跳转，让用户看到成功提示
        setTimeout(() => {
          uni.switchTab({
            url: "/pages/index/index",
          });
        }, 1500);
      } catch (error) {
        console.error("登录失败:", error);
        uni.showToast({
          title: error.message || "登录失败",
          icon: "none",
        });
      } finally {
        this.isLogging = false;
      }
    },

    // 模拟登录请求
    mockLogin() {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          // 模拟登录成功
          if (
            this.loginForm.username === "admin" &&
            this.loginForm.password === "123456"
          ) {
            resolve();
          } else {
            reject(new Error("用户名或密码错误"));
          }
        }, 1000);
      });
    },

    // 跳转到注册页面
    goToRegister() {
      uni.navigateTo({
        url: "/pages/register/register",
      });
    },
  },
};
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0 60rpx;
  display: flex;
  flex-direction: column;
}

/* 顶部装饰 */
.header-decoration {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 0 80rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.app-name {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
}

.welcome-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 登录表单 */
.login-form {
  flex: 1;
  background-color: white;
  border-radius: 30rpx 30rpx 0 0;
  padding: 60rpx 40rpx 40rpx;
  margin-top: 40rpx;
}

.form-group {
  margin-bottom: 40rpx;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 15rpx;
  padding: 0 20rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #667eea;
  background-color: white;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}

.input-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 20rpx;
}

.form-input {
  flex: 1;
  height: 88rpx;
  font-size: 28rpx;
  color: #333;
  background-color: transparent;
}

.password-toggle {
  width: 32rpx;
  height: 32rpx;
  margin-left: 20rpx;
}

.error-text {
  font-size: 24rpx;
  color: #ff4757;
  margin-top: 10rpx;
  margin-left: 20rpx;
}

/* 表单选项 */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 60rpx;
}

.remember-me {
  display: flex;
  align-items: center;
}

.checkbox {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

.checkbox-text {
  font-size: 26rpx;
  color: #666;
}

.forgot-password {
  font-size: 26rpx;
  color: #667eea;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 15rpx;
  border: none;
  margin-bottom: 40rpx;
  transition: all 0.3s ease;
}

.login-btn:active {
  transform: scale(0.98);
}

.login-btn-disabled {
  background: #ccc;
  color: #999;
}

/* 分割线 */
.divider {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.divider-line {
  flex: 1;
  height: 1rpx;
  background-color: #e0e0e0;
}

.divider-text {
  font-size: 24rpx;
  color: #999;
  margin: 0 20rpx;
}

/* 注册提示 */
.register-tip {
  text-align: center;
}

.tip-text {
  font-size: 26rpx;
  color: #666;
}

.register-link {
  font-size: 26rpx;
  color: #667eea;
  margin-left: 10rpx;
}
</style>
