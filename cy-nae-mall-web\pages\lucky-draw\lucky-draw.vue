<template>
  <view class="lucky-draw-page">
    <!-- 顶部导航 -->
    <view class="navbar">
      <view class="nav-left" @click="goBack">
        <text class="nav-icon">←</text>
      </view>
      <text class="nav-title">幸运大抽奖</text>
      <view class="nav-right"></view>
    </view>

    <!-- 抽奖背景装饰 -->
    <view class="background-decoration">
      <view class="star star-1">⭐</view>
      <view class="star star-2">✨</view>
      <view class="star star-3">🌟</view>
      <view class="star star-4">💫</view>
      <view class="star star-5">⭐</view>
      <view class="star star-6">✨</view>
    </view>

    <!-- 抽奖转盘 -->
    <view class="lottery-container">
      <view class="lottery-wheel" :class="{ 'spinning': isSpinning }">
        <view class="wheel-center">
          <text class="center-text">抽奖</text>
        </view>
        <view class="wheel-item" v-for="(prize, index) in prizes" :key="index" :style="getItemStyle(index)">
          <text class="prize-text">{{ prize.name }}</text>
        </view>
      </view>
      <view class="lottery-pointer">
        <text class="pointer-icon">📍</text>
      </view>
    </view>

    <!-- 抽奖按钮 -->
    <view class="draw-button-container">
      <button class="draw-button" :disabled="!canDraw || isSpinning" @click="startDraw">
        <text class="button-text">{{ buttonText }}</text>
      </button>
      <view class="draw-info">
        <text class="info-text">今日剩余抽奖次数：{{ remainingTimes }}</text>
      </view>
    </view>

    <!-- 奖品展示 -->
    <view class="prizes-display">
      <view class="section-title">
        <text class="title-text">丰厚奖品</text>
      </view>
      <view class="prizes-grid">
        <view class="prize-item" v-for="(prize, index) in prizes" :key="index">
          <image class="prize-image" :src="prize.image" mode="aspectFill"></image>
          <text class="prize-name">{{ prize.name }}</text>
          <text class="prize-desc">{{ prize.description }}</text>
        </view>
      </view>
    </view>

    <!-- 中奖记录 -->
    <view class="winning-records">
      <view class="section-title">
        <text class="title-text">中奖记录</text>
      </view>
      <view class="records-list">
        <view class="record-item" v-for="(record, index) in winningRecords" :key="index">
          <text class="record-user">{{ record.user }}</text>
          <text class="record-prize">{{ record.prize }}</text>
          <text class="record-time">{{ record.time }}</text>
        </view>
      </view>
    </view>

    <!-- 抽奖结果弹窗 -->
    <view class="result-modal" v-if="showResult" @click="closeResult">
      <view class="modal-content" @click.stop>
        <view class="result-header">
          <text class="result-title">🎉 恭喜您！</text>
        </view>
        <view class="result-body">
          <image class="result-image" :src="currentPrize.image" mode="aspectFill"></image>
          <text class="result-name">{{ currentPrize.name }}</text>
          <text class="result-desc">{{ currentPrize.description }}</text>
        </view>
        <view class="result-footer">
          <button class="confirm-button" @click="closeResult">
            <text class="confirm-text">确定</text>
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      isSpinning: false,
      showResult: false,
      remainingTimes: 3,
      currentPrize: {},
      prizes: [
        {
          id: 1,
          name: 'iPhone15 Pro',
          description: '256GB 深空黑色',
          image: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=300&h=300&fit=crop',
          probability: 0.01
        },
        {
          id: 2,
          name: '华为Mate60',
          description: '512GB 雅川青',
          image: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=300&h=300&fit=crop',
          probability: 0.01
        },
        {
          id: 3,
          name: '100元购物券',
          description: '全场通用无门槛',
          image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=300&h=300&fit=crop',
          probability: 0.1
        },
        {
          id: 4,
          name: '50元购物券',
          description: '满200元可用',
          image: 'https://images.unsplash.com/photo-1567581935884-3349723552ca?w=300&h=300&fit=crop',
          probability: 0.2
        },
        {
          id: 5,
          name: '20元购物券',
          description: '满100元可用',
          image: 'https://images.unsplash.com/photo-1580910051074-3eb694886505?w=300&h=300&fit=crop',
          probability: 0.3
        },
        {
          id: 6,
          name: '谢谢参与',
          description: '下次再来',
          image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=300&h=300&fit=crop',
          probability: 0.38
        }
      ],
      winningRecords: [
        { user: '138****8888', prize: 'iPhone15 Pro', time: '2分钟前' },
        { user: '139****9999', prize: '100元购物券', time: '5分钟前' },
        { user: '136****6666', prize: '华为Mate60', time: '8分钟前' },
        { user: '137****7777', prize: '50元购物券', time: '12分钟前' },
        { user: '135****5555', prize: '20元购物券', time: '15分钟前' }
      ]
    };
  },
  
  computed: {
    canDraw() {
      return this.remainingTimes > 0;
    },
    
    buttonText() {
      if (this.isSpinning) {
        return '抽奖中...';
      } else if (!this.canDraw) {
        return '今日次数已用完';
      } else {
        return '开始抽奖';
      }
    }
  },
  
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 获取奖品项样式
    getItemStyle(index) {
      const angle = (360 / this.prizes.length) * index;
      return {
        transform: `rotate(${angle}deg)`,
        transformOrigin: '50% 200rpx'
      };
    },
    
    // 开始抽奖
    startDraw() {
      if (!this.canDraw || this.isSpinning) return;
      
      this.isSpinning = true;
      this.remainingTimes--;
      
      // 模拟抽奖逻辑
      const randomPrize = this.getRandomPrize();
      this.currentPrize = randomPrize;
      
      // 抽奖动画持续3秒
      setTimeout(() => {
        this.isSpinning = false;
        this.showResult = true;
        
        // 添加中奖记录
        this.addWinningRecord(randomPrize);
      }, 3000);
    },
    
    // 获取随机奖品
    getRandomPrize() {
      const random = Math.random();
      let cumulativeProbability = 0;
      
      for (let prize of this.prizes) {
        cumulativeProbability += prize.probability;
        if (random <= cumulativeProbability) {
          return prize;
        }
      }
      
      return this.prizes[this.prizes.length - 1]; // 默认返回最后一个
    },
    
    // 添加中奖记录
    addWinningRecord(prize) {
      const newRecord = {
        user: '138****' + Math.floor(Math.random() * 10000).toString().padStart(4, '0'),
        prize: prize.name,
        time: '刚刚'
      };
      
      this.winningRecords.unshift(newRecord);
      if (this.winningRecords.length > 10) {
        this.winningRecords.pop();
      }
    },
    
    // 关闭结果弹窗
    closeResult() {
      this.showResult = false;
    }
  }
};
</script>

<style scoped>
.lucky-draw-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

/* 导航栏 */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 88rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  z-index: 1000;
}

.nav-left, .nav-right {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
}

.nav-icon {
  font-size: 32rpx;
  color: white;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.star {
  position: absolute;
  font-size: 30rpx;
  animation: twinkle 3s ease-in-out infinite;
}

.star-1 { top: 15%; left: 10%; animation-delay: 0s; }
.star-2 { top: 25%; right: 15%; animation-delay: 0.5s; }
.star-3 { top: 45%; left: 5%; animation-delay: 1s; }
.star-4 { top: 65%; right: 10%; animation-delay: 1.5s; }
.star-5 { top: 80%; left: 20%; animation-delay: 2s; }
.star-6 { top: 35%; right: 25%; animation-delay: 2.5s; }

@keyframes twinkle {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}
</style>
