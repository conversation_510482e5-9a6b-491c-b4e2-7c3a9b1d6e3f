<template>
	<view class="mine-page">
		<!-- 个人信息区域 -->
		<view class="user-info-section">
			<view class="user-bg"></view>
			<view class="user-content">
				<view class="user-avatar" @click="editProfile">
					<image v-if="userInfo.avatar" :src="userInfo.avatar" mode="aspectFill"></image>
					<view v-else class="default-avatar">
						<text class="avatar-text">{{userInfo.nickname ? userInfo.nickname.charAt(0) : '用'}}</text>
					</view>
				</view>
				<view class="user-details">
					<view class="user-name" v-if="isLoggedIn" @click="editProfile">
						{{userInfo.nickname || userInfo.username || '用户'}}
					</view>
					<view class="user-name" v-else @click="goToLogin">
						点击登录
					</view>
					<view class="user-phone" v-if="isLoggedIn && userInfo.phone">
						{{userInfo.phone}}
					</view>
					<view class="login-tip" v-else>
						登录后享受更多服务
					</view>
				</view>
				<view class="user-actions">
					<view class="action-btn" v-if="isLoggedIn" @click="goToLogin">
						<text class="action-text">设置</text>
					</view>
					<view class="action-btn" v-else @click="goToLogin">
						<text class="action-text">登录</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 我的订单区域 -->
		<view class="orders-section">
			<view class="section-header" @click="goToOrders">
				<text class="section-title">我的订单</text>
				<text class="section-more">查看全部 ></text>
			</view>
			<view class="order-status-grid">
				<view class="order-status-item" @click="goToOrders('pending')">
					<view class="status-icon">
						<image src="/static/my/odzf.png" mode="aspectFit"></image>
						<view class="badge" v-if="orderCounts.pending > 0">{{orderCounts.pending}}</view>
					</view>
					<text class="status-text">待支付</text>
				</view>
				<view class="order-status-item" @click="goToOrders('delivery')">
					<view class="status-icon">
						<image src="/static/my/osh.png" mode="aspectFit"></image>
						<view class="badge" v-if="orderCounts.delivery > 0">{{orderCounts.delivery}}</view>
					</view>
					<text class="status-text">待提货</text>
				</view>
				<view class="order-status-item" @click="goToOrders('completed')">
					<view class="status-icon">
						<image src="/static/my/odth.png" mode="aspectFit"></image>
					</view>
					<text class="status-text">已完成</text>
				</view>
				<view class="order-status-item" @click="goToOrders('aftersale')">
					<view class="status-icon">
						<image src="/static/my/aftersale.png" mode="aspectFit"></image>
						<view class="badge" v-if="orderCounts.aftersale > 0">{{orderCounts.aftersale}}</view>
					</view>
					<text class="status-text">售后</text>
				</view>
			</view>
		</view>

		<!-- 功能列表区域 -->
		<view class="function-section">
			<view class="function-group">
				<view class="function-item" @click="goToPage('/pages/about/about')">
					<view class="function-left">
						<image class="function-icon" src="/static/my/about.png"></image>
						<text class="function-text">关于我们</text>
					</view>
					<text class="function-arrow">></text>
				</view>
				<view class="function-item" @click="goToPage('/pages/privacy/privacy')">
					<view class="function-left">
						<image class="function-icon" src="/static/my/privacy.png"></image>
						<text class="function-text">用户隐私</text>
					</view>
					<text class="function-arrow">></text>
				</view>
			</view>
			
			<view class="function-group">
				<view class="function-item" @click="goToPage('/pages/warranty/warranty')">
					<view class="function-left">
						<image class="function-icon" src="/static/my/zhibao.png"></image>
						<text class="function-text">质保服务</text>
					</view>
					<text class="function-arrow">></text>
				</view>
				<view class="function-item" @click="goToPage('/pages/manual/manual')">
					<view class="function-left">
						<image class="function-icon" src="/static/my/yinsicelve.png"></image>
						<text class="function-text">产品说明书</text>
					</view>
					<text class="function-arrow">></text>
				</view>
			</view>

			<view class="function-group" v-if="isLoggedIn">
				<view class="function-item logout-item" @click="logout">
					<view class="function-left">
						<image class="function-icon" src="/static/my/baseinfo.png"></image>
						<text class="function-text logout-text">退出登录</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 用户信息
				userInfo: {
					id: null,
					username: '',
					nickname: '',
					phone: '',
					avatar: '',
					email: ''
				},
				// 登录状态
				isLoggedIn: false,
				// 订单数量统计
				orderCounts: {
					pending: 0,    // 待支付
					delivery: 0,   // 待提货
					completed: 0,  // 已完成
					aftersale: 0   // 售后
				}
			}
		},
		onLoad() {
			this.checkLoginStatus();
			this.loadUserInfo();
			this.loadOrderCounts();
		},
		onShow() {
			// 页面显示时刷新数据
			this.checkLoginStatus();
			if (this.isLoggedIn) {
				this.loadUserInfo();
				this.loadOrderCounts();
			}
		},
		methods: {
			// 检查登录状态
			checkLoginStatus() {
				// TODO: 从本地存储或token验证登录状态
				const token = uni.getStorageSync('token');
				this.isLoggedIn = !!token;
				
				if (this.isLoggedIn) {
					// 从本地存储获取用户信息
					const userInfo = uni.getStorageSync('userInfo');
					if (userInfo) {
						this.userInfo = userInfo;
					}
				}
			},
			
			// 加载用户信息
			loadUserInfo() {
				if (!this.isLoggedIn) return;
				
				// TODO: 调用后端API获取用户信息
				console.log('加载用户信息');
			},
			
			// 加载订单数量统计
			loadOrderCounts() {
				if (!this.isLoggedIn) return;
				
				// TODO: 调用后端API获取订单统计
				console.log('加载订单统计');
				
				// 模拟数据
				this.orderCounts = {
					pending: 2,
					delivery: 1,
					completed: 5,
					aftersale: 0
				};
			},
			
			// 跳转到登录页面
			goToLogin() {
				uni.navigateTo({
					url: '/pages/login/login'
				});
			},
			
			// 编辑个人资料
			editProfile() {
				if (!this.isLoggedIn) {
					this.goToLogin();
					return;
				}
				
				uni.navigateTo({
					url: '/pages/profile/edit'
				});
			},
			
			// 跳转到订单页面
			goToOrders(status = '') {
				if (!this.isLoggedIn) {
					this.goToLogin();
					return;
				}
				
				let url = '/pages/orders/orders';
				if (status) {
					url += `?status=${status}`;
				}
				
				uni.navigateTo({
					url: url
				});
			},
			
			// 跳转到指定页面
			goToPage(url) {
				uni.navigateTo({
					url: url
				});
			},
			
			// 退出登录
			logout() {
				uni.showModal({
					title: '提示',
					content: '确定要退出登录吗？',
					success: (res) => {
						if (res.confirm) {
							// 清除本地存储
							uni.removeStorageSync('token');
							uni.removeStorageSync('userInfo');
							
							// 重置状态
							this.isLoggedIn = false;
							this.userInfo = {
								id: null,
								username: '',
								nickname: '',
								phone: '',
								avatar: '',
								email: ''
							};
							this.orderCounts = {
								pending: 0,
								delivery: 0,
								completed: 0,
								aftersale: 0
							};
							
							uni.showToast({
								title: '已退出登录',
								icon: 'success'
							});
						}
					}
				});
			}
		}
	}
</script>

<style scoped>
	.mine-page {
		background-color: #f8f8f8;
		min-height: 100vh;
	}

	/* 个人信息区域 */
	.user-info-section {
		position: relative;
		height: 300rpx;
		overflow: hidden;
	}

	.user-bg {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	}

	.user-content {
		position: relative;
		z-index: 2;
		display: flex;
		align-items: center;
		padding: 60rpx 30rpx 30rpx;
		height: 100%;
	}

	.user-avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 60rpx;
		overflow: hidden;
		margin-right: 30rpx;
		border: 4rpx solid rgba(255, 255, 255, 0.3);
	}

	.user-avatar image {
		width: 100%;
		height: 100%;
	}

	.default-avatar {
		width: 100%;
		height: 100%;
		background-color: rgba(255, 255, 255, 0.2);
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.avatar-text {
		font-size: 48rpx;
		color: white;
		font-weight: bold;
	}

	.user-details {
		flex: 1;
	}

	.user-name {
		font-size: 36rpx;
		color: white;
		font-weight: bold;
		margin-bottom: 10rpx;
	}

	.user-phone {
		font-size: 26rpx;
		color: rgba(255, 255, 255, 0.8);
	}

	.login-tip {
		font-size: 26rpx;
		color: rgba(255, 255, 255, 0.8);
	}

	.user-actions {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.action-btn {
		background-color: rgba(255, 255, 255, 0.2);
		padding: 15rpx 30rpx;
		border-radius: 30rpx;
		border: 2rpx solid rgba(255, 255, 255, 0.3);
	}

	.action-text {
		font-size: 26rpx;
		color: white;
	}

	/* 我的订单区域 */
	.orders-section {
		background-color: white;
		margin: 20rpx;
		border-radius: 20rpx;
		padding: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
		padding-bottom: 20rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}

	.section-more {
		font-size: 26rpx;
		color: #999;
	}

	.order-status-grid {
		display: flex;
		justify-content: space-around;
	}

	.order-status-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		flex: 1;
		position: relative;
	}

	.status-icon {
		position: relative;
		width: 80rpx;
		height: 80rpx;
		margin-bottom: 15rpx;
	}

	.status-icon image {
		width: 100%;
		height: 100%;
	}

	.badge {
		position: absolute;
		top: -10rpx;
		right: -10rpx;
		background-color: #ff4757;
		color: white;
		font-size: 20rpx;
		padding: 4rpx 8rpx;
		border-radius: 20rpx;
		min-width: 30rpx;
		text-align: center;
		line-height: 1;
	}

	.status-text {
		font-size: 24rpx;
		color: #666;
		text-align: center;
	}

	/* 功能列表区域 */
	.function-section {
		margin: 20rpx;
	}

	.function-group {
		background-color: white;
		border-radius: 20rpx;
		margin-bottom: 20rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.function-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
		transition: background-color 0.3s ease;
	}

	.function-item:last-child {
		border-bottom: none;
	}

	.function-item:active {
		background-color: #f8f9fa;
	}

	.function-left {
		display: flex;
		align-items: center;
	}

	.function-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 20rpx;
	}

	.function-text {
		font-size: 28rpx;
		color: #333;
	}

	.logout-text {
		color: #ff4757;
	}

	.function-arrow {
		font-size: 24rpx;
		color: #ccc;
	}

	.logout-item .function-arrow {
		display: none;
	}
</style>
