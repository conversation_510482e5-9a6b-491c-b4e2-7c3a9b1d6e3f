<template>
  <view class="product-detail">
    <!-- 顶部导航栏 -->
    <view class="navbar">
      <view class="nav-left" @click="goBack">
        <text class="nav-icon">←</text>
      </view>
      <text class="nav-title">商品详情</text>
      <view class="nav-right" @click="toggleFavorite">
        <text class="nav-icon">{{ isFavorite ? "❤️" : "🤍" }}</text>
      </view>
    </view>

    <!-- 产品图片轮播 -->
    <view class="product-images">
      <swiper
        class="image-swiper"
        :indicator-dots="true"
        :autoplay="false"
        :duration="300"
        indicator-color="rgba(255, 255, 255, 0.5)"
        indicator-active-color="#667eea"
      >
        <swiper-item v-for="(image, index) in product.images" :key="index">
          <image
            class="product-image"
            :src="image"
            mode="aspectFill"
            @click="previewImage(image)"
          ></image>
        </swiper-item>
      </swiper>
      <view class="image-count">
        <text>{{ currentImageIndex + 1 }}/{{ product.images.length }}</text>
      </view>
    </view>

    <!-- 产品基本信息 -->
    <view class="product-info">
      <view class="product-header">
        <text class="product-name">{{ product.name }}</text>
        <view class="product-tags">
          <text class="tag hot" v-if="product.isHot">热销</text>
          <text class="tag new" v-if="product.isNew">新品</text>
          <text class="tag discount" v-if="product.hasDiscount">特惠</text>
        </view>
      </view>

      <view class="product-price">
        <text class="current-price">¥{{ product.price }}</text>
        <text class="original-price" v-if="product.originalPrice"
          >¥{{ product.originalPrice }}</text
        >
        <view class="price-badge" v-if="product.hasDiscount">
          <text class="discount-text"
            >立省¥{{ (product.originalPrice - product.price).toFixed(0) }}</text
          >
        </view>
      </view>

      <text class="product-description">{{ product.description }}</text>

      <view class="product-stats">
        <view class="stat-item">
          <text class="stat-label">销量</text>
          <text class="stat-value">{{ product.salesCount }}+</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">评价</text>
          <text class="stat-value">{{ product.rating || "4.8" }}分</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">库存</text>
          <text class="stat-value">{{ product.stock || "充足" }}</text>
        </view>
      </view>
    </view>

    <!-- 产品详情图片 -->
    <view class="product-details">
      <view class="section-title">
        <text class="title-text">产品详情</text>
        <view class="title-line"></view>
      </view>

      <view class="detail-images">
        <image
          v-for="(detailImage, index) in product.detailImages"
          :key="index"
          class="detail-image"
          :src="detailImage"
          mode="widthFix"
          @click="previewImage(detailImage)"
        ></image>
      </view>
    </view>

    <!-- 产品规格参数 -->
    <view class="product-specs" v-if="product.specifications">
      <view class="section-title">
        <text class="title-text">规格参数</text>
        <view class="title-line"></view>
      </view>

      <view class="specs-list">
        <view
          class="spec-item"
          v-for="(spec, index) in product.specifications"
          :key="index"
        >
          <text class="spec-label">{{ spec.label }}</text>
          <text class="spec-value">{{ spec.value }}</text>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <view class="action-left">
        <view class="action-item" @click="contactService">
          <text class="action-icon">💬</text>
          <text class="action-text">客服</text>
        </view>
        <view class="action-item" @click="goToInstallment">
          <text class="action-icon">💳</text>
          <text class="action-text">分期</text>
        </view>
      </view>

      <view class="action-right">
        <button class="btn-cart" @click="addToCart">
          <text class="btn-text">加入购物车</text>
        </button>
        <button class="btn-buy" @click="buyNow">
          <text class="btn-text">立即购买</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      productId: "",
      currentImageIndex: 0,
      isFavorite: false,
      product: {
        id: 1,
        name: "智能手机 Pro Max",
        description:
          "5G旗舰机型，拍照更清晰，性能更强劲，为您带来极致的使用体验",
        price: 2999.0,
        originalPrice: 3299.0,
        salesCount: 1250,
        rating: 4.9,
        stock: "充足",
        isHot: true,
        isNew: false,
        hasDiscount: true,
        images: [
          "https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=600&h=600&fit=crop",
          "https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=600&h=600&fit=crop",
          "https://images.unsplash.com/photo-1580910051074-3eb694886505?w=600&h=600&fit=crop",
          "https://images.unsplash.com/photo-1567581935884-3349723552ca?w=600&h=600&fit=crop",
        ],
        detailImages: [
          "https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=800&h=600&fit=crop",
          "https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=800&h=600&fit=crop",
          "https://images.unsplash.com/photo-1580910051074-3eb694886505?w=800&h=600&fit=crop",
          "https://images.unsplash.com/photo-1567581935884-3349723552ca?w=800&h=600&fit=crop",
          "https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=800&h=400&fit=crop",
          "https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=800&h=400&fit=crop",
        ],
        specifications: [
          { label: "屏幕尺寸", value: "6.7英寸" },
          { label: "分辨率", value: "2778×1284像素" },
          { label: "处理器", value: "A17 Pro芯片" },
          { label: "存储容量", value: "256GB" },
          { label: "摄像头", value: "4800万像素三摄" },
          { label: "电池容量", value: "4422mAh" },
          { label: "充电功率", value: "27W无线充电" },
          { label: "操作系统", value: "iOS 17" },
          { label: "网络制式", value: "5G全网通" },
          { label: "机身重量", value: "221g" },
        ],
      },
    };
  },

  onLoad(options) {
    if (options.id) {
      this.productId = options.id;
      this.loadProductDetail();
    }
  },

  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 切换收藏状态
    toggleFavorite() {
      this.isFavorite = !this.isFavorite;
      uni.showToast({
        title: this.isFavorite ? "已收藏" : "已取消收藏",
        icon: "success",
        duration: 1500,
      });
    },

    // 预览图片
    previewImage(current) {
      const urls = [...this.product.images, ...this.product.detailImages];
      uni.previewImage({
        current: current,
        urls: urls,
      });
    },

    // 加载产品详情
    loadProductDetail() {
      // TODO: 根据productId从API获取产品详情
      console.log("加载产品详情:", this.productId);
    },

    // 联系客服
    contactService() {
      uni.showToast({
        title: "正在连接客服...",
        icon: "loading",
      });
    },

    // 跳转到分期页面
    goToInstallment() {
      uni.navigateTo({
        url: "/pages/installment/installment",
      });
    },

    // 加入购物车
    addToCart() {
      uni.showToast({
        title: "已加入购物车",
        icon: "success",
      });
    },

    // 立即购买
    buyNow() {
      uni.showToast({
        title: "正在跳转支付...",
        icon: "loading",
      });
    },
  },
};
</script>

<style scoped>
.product-detail {
  background: #f8f9fa;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 导航栏 */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 88rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  z-index: 1000;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.nav-left,
.nav-right {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.05);
}

.nav-icon {
  font-size: 32rpx;
  color: #333;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 产品图片 */
.product-images {
  position: relative;
  margin-top: 88rpx;
}

.image-swiper {
  width: 100%;
  height: 750rpx;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background: #f5f5f5;
}

.image-count {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

/* 产品信息 */
.product-info {
  background: white;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.product-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  line-height: 1.4;
}

.product-tags {
  display: flex;
  gap: 8rpx;
  margin-left: 20rpx;
}

.tag {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 600;
  color: white;
}

.tag.hot {
  background: linear-gradient(135deg, #ff6b6b, #ff8e53);
}

.tag.new {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
}

.tag.discount {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.product-price {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  flex-wrap: wrap;
  gap: 15rpx;
}

.current-price {
  font-size: 48rpx;
  font-weight: bold;
  color: #ff6b6b;
}

.original-price {
  font-size: 28rpx;
  color: #999;
  text-decoration: line-through;
}

.price-badge {
  background: linear-gradient(135deg, #ff6b6b, #ff8e53);
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

.discount-text {
  color: white;
  font-size: 20rpx;
  font-weight: 600;
}

.product-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 25rpx;
}

.product-stats {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  border-top: 1rpx solid #f0f0f0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

.stat-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

/* 详情区域 */
.product-details,
.product-specs {
  background: white;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 20rpx;
}

.title-line {
  flex: 1;
  height: 2rpx;
  background: linear-gradient(90deg, #667eea, transparent);
}

.detail-images {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.detail-image {
  width: 100%;
  border-radius: 12rpx;
  background: #f5f5f5;
}

/* 规格参数 */
.specs-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.spec-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.spec-item:last-child {
  border-bottom: none;
}

.spec-label {
  font-size: 28rpx;
  color: #666;
  flex: 1;
}

.spec-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  text-align: right;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.action-left {
  display: flex;
  gap: 30rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
  cursor: pointer;
}

.action-icon {
  font-size: 32rpx;
}

.action-text {
  font-size: 20rpx;
  color: #666;
}

.action-right {
  display: flex;
  gap: 20rpx;
}

.btn-cart,
.btn-buy {
  padding: 20rpx 40rpx;
  border-radius: 25rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-cart {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  color: #333;
  border: 2rpx solid #667eea;
}

.btn-buy {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
}

.btn-cart:active {
  transform: scale(0.95);
}

.btn-buy:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 10rpx rgba(102, 126, 234, 0.4);
}

.btn-text {
  color: inherit;
  font-size: inherit;
  font-weight: inherit;
}
</style>
