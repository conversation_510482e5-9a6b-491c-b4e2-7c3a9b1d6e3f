<template>
  <view class="register-page">
    <!-- 背景装饰 -->
    <view class="background-decoration">
      <view class="decoration-circle circle-1"></view>
      <view class="decoration-circle circle-2"></view>
      <view class="decoration-circle circle-3"></view>
    </view>

    <!-- 顶部区域 -->
    <view class="header-section">
      <view class="logo-container">
        <image class="logo" src="/static/logo.png" mode="aspectFit"></image>
        <view class="logo-glow"></view>
      </view>
      <text class="app-name">传艺商城</text>
      <text class="welcome-text">世界品牌，中国传艺</text>
      <text class="subtitle">创建您的专属账户</text>
    </view>

    <!-- 注册表单卡片 -->
    <view class="form-card">
      <view class="card-header">
        <text class="form-title">账户注册</text>
        <view class="title-underline"></view>
      </view>

      <view class="form-content">
        <view class="form-group">
          <view class="input-container">
            <view
              class="input-wrapper"
              :class="{
                'input-focused': focusedInput === 'phone',
                'input-error': errors.phone,
              }"
            >
              <view class="input-icon-wrapper">
                <text class="input-icon">📱</text>
              </view>
              <input
                class="form-input"
                type="number"
                placeholder="手机号"
                v-model="registerForm.phone"
                @focus="focusedInput = 'phone'"
                @blur="
                  focusedInput = '';
                  validatePhone();
                "
                maxlength="11"
              />
            </view>
            <view class="error-message" v-if="errors.phone">
              <text class="error-text">{{ errors.phone }}</text>
            </view>
          </view>
        </view>

        <view class="form-group">
          <view class="input-container">
            <view
              class="input-wrapper"
              :class="{
                'input-focused': focusedInput === 'password',
                'input-error': errors.password,
              }"
            >
              <view class="input-icon-wrapper">
                <text class="input-icon">🔒</text>
              </view>
              <input
                class="form-input"
                :type="showPassword ? 'text' : 'password'"
                placeholder="设置密码（6-20位）"
                v-model="registerForm.password"
                @focus="focusedInput = 'password'"
                @blur="
                  focusedInput = '';
                  validatePassword();
                "
                maxlength="20"
              />
              <view class="password-toggle" @click="togglePassword">
                <text class="toggle-icon">{{
                  showPassword ? "👁️" : "👁️‍🗨️"
                }}</text>
              </view>
            </view>
            <view class="error-message" v-if="errors.password">
              <text class="error-text">{{ errors.password }}</text>
            </view>
          </view>
        </view>

        <view class="form-group">
          <view class="input-container">
            <view
              class="input-wrapper"
              :class="{
                'input-focused': focusedInput === 'confirmPassword',
                'input-error': errors.confirmPassword,
              }"
            >
              <view class="input-icon-wrapper">
                <text class="input-icon">🔐</text>
              </view>
              <input
                class="form-input"
                :type="showConfirmPassword ? 'text' : 'password'"
                placeholder="确认密码"
                v-model="registerForm.confirmPassword"
                @focus="focusedInput = 'confirmPassword'"
                @blur="
                  focusedInput = '';
                  validateConfirmPassword();
                "
                maxlength="20"
              />
              <view class="password-toggle" @click="toggleConfirmPassword">
                <text class="toggle-icon">{{
                  showConfirmPassword ? "👁️" : "👁️‍🗨️"
                }}</text>
              </view>
            </view>
            <view class="error-message" v-if="errors.confirmPassword">
              <text class="error-text">{{ errors.confirmPassword }}</text>
            </view>
          </view>
        </view>

        <view class="optional-fields">
          <view class="form-group">
            <view class="input-container">
              <view
                class="input-wrapper"
                :class="{
                  'input-focused': focusedInput === 'name',
                  'input-error': errors.name,
                }"
              >
                <view class="input-icon-wrapper">
                  <text class="input-icon">👤</text>
                </view>
                <input
                  class="form-input"
                  type="text"
                  placeholder="姓名（选填）"
                  v-model="registerForm.name"
                  @focus="focusedInput = 'name'"
                  @blur="
                    focusedInput = '';
                    validateName();
                  "
                  maxlength="20"
                />
              </view>
              <view class="error-message" v-if="errors.name">
                <text class="error-text">{{ errors.name }}</text>
              </view>
            </view>
          </view>

          <view class="form-group">
            <view class="input-container">
              <view
                class="input-wrapper"
                :class="{
                  'input-focused': focusedInput === 'storeCode',
                  'input-error': errors.storeCode,
                }"
              >
                <view class="input-icon-wrapper">
                  <text class="input-icon">🏪</text>
                </view>
                <input
                  class="form-input"
                  type="text"
                  placeholder="门店编号（选填）"
                  v-model="registerForm.storeCode"
                  @focus="focusedInput = 'storeCode'"
                  @blur="
                    focusedInput = '';
                    validateStoreCode();
                  "
                  maxlength="20"
                />
              </view>
              <view class="error-message" v-if="errors.storeCode">
                <text class="error-text">{{ errors.storeCode }}</text>
              </view>
            </view>
          </view>
        </view>

        <view class="agreement-section">
          <view class="agreement-wrapper" @click="toggleAgreement">
            <view
              class="custom-checkbox"
              :class="{ 'checkbox-checked': agreedToTerms }"
            >
              <text class="checkbox-icon" v-if="agreedToTerms">✓</text>
            </view>
            <text class="agreement-text">
              我已阅读并同意
              <text class="agreement-link" @click.stop="viewUserAgreement"
                >《用户协议》</text
              >
              和
              <text class="agreement-link" @click.stop="viewPrivacyPolicy"
                >《隐私政策》</text
              >
            </text>
          </view>
        </view>

        <button
          class="register-button"
          :class="{
            'button-disabled': !canRegister,
            'button-loading': isRegistering,
          }"
          :disabled="!canRegister"
          @click="handleRegister"
        >
          <view class="button-content">
            <view class="loading-spinner" v-if="isRegistering"></view>
            <text class="button-text">{{
              isRegistering ? "注册中..." : "立即注册"
            }}</text>
          </view>
        </button>

        <view class="login-section">
          <text class="login-text">已有账号？</text>
          <text class="login-link" @click="goToLogin">立即登录</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 注册表单数据
      registerForm: {
        phone: "",
        password: "",
        confirmPassword: "",
        name: "",
        storeCode: "",
      },
      // 表单验证错误
      errors: {
        phone: "",
        password: "",
        confirmPassword: "",
        name: "",
        storeCode: "",
      },
      // 是否显示密码
      showPassword: false,
      showConfirmPassword: false,
      // 是否同意协议
      agreedToTerms: false,
      // 是否正在注册
      isRegistering: false,
      // 当前聚焦的输入框
      focusedInput: "",
    };
  },
  computed: {
    // 是否可以注册
    canRegister() {
      return (
        this.registerForm.phone.trim() &&
        this.registerForm.password.trim() &&
        this.registerForm.confirmPassword.trim() &&
        !this.errors.phone &&
        !this.errors.password &&
        !this.errors.confirmPassword &&
        this.agreedToTerms &&
        !this.isRegistering
      );
    },
  },
  methods: {
    // 验证手机号
    validatePhone() {
      const phone = this.registerForm.phone.trim();
      if (!phone) {
        this.errors.phone = "请输入手机号";
        return false;
      }

      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(phone)) {
        this.errors.phone = "请输入正确的手机号";
        return false;
      }

      this.errors.phone = "";
      return true;
    },

    // 验证密码
    validatePassword() {
      const password = this.registerForm.password.trim();
      if (!password) {
        this.errors.password = "请输入密码";
        return false;
      }

      if (password.length < 6) {
        this.errors.password = "密码至少6位";
        return false;
      }

      if (password.length > 20) {
        this.errors.password = "密码最多20位";
        return false;
      }

      // 密码强度验证（至少包含字母和数字）
      const strongPasswordRegex =
        /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{6,}$/;
      if (!strongPasswordRegex.test(password)) {
        this.errors.password = "密码需包含字母和数字";
        return false;
      }

      this.errors.password = "";
      return true;
    },

    // 验证确认密码
    validateConfirmPassword() {
      const confirmPassword = this.registerForm.confirmPassword.trim();
      if (!confirmPassword) {
        this.errors.confirmPassword = "请确认密码";
        return false;
      }

      if (confirmPassword !== this.registerForm.password) {
        this.errors.confirmPassword = "两次输入的密码不一致";
        return false;
      }

      this.errors.confirmPassword = "";
      return true;
    },

    // 验证姓名
    validateName() {
      const name = this.registerForm.name.trim();
      if (name && name.length > 20) {
        this.errors.name = "姓名最多20个字符";
        return false;
      }

      this.errors.name = "";
      return true;
    },

    // 验证门店编号
    validateStoreCode() {
      const storeCode = this.registerForm.storeCode.trim();
      if (storeCode && storeCode.length > 20) {
        this.errors.storeCode = "门店编号最多20个字符";
        return false;
      }

      this.errors.storeCode = "";
      return true;
    },

    // 切换密码显示
    togglePassword() {
      this.showPassword = !this.showPassword;
    },

    // 切换确认密码显示
    toggleConfirmPassword() {
      this.showConfirmPassword = !this.showConfirmPassword;
    },

    // 切换协议同意状态
    toggleAgreement() {
      this.agreedToTerms = !this.agreedToTerms;
    },

    // 查看用户协议
    viewUserAgreement() {
      uni.navigateTo({
        url: "/pages/agreement/user",
      });
    },

    // 查看隐私政策
    viewPrivacyPolicy() {
      uni.navigateTo({
        url: "/pages/privacy/privacy",
      });
    },

    // 处理注册
    async handleRegister() {
      if (
        !this.validatePhone() ||
        !this.validatePassword() ||
        !this.validateConfirmPassword()
      ) {
        return;
      }

      if (!this.agreedToTerms) {
        uni.showToast({
          title: "请先同意用户协议和隐私政策",
          icon: "none",
        });
        return;
      }

      this.isRegistering = true;

      try {
        // TODO: 调用注册API
        console.log("注册请求:", this.registerForm);

        // 模拟注册请求
        await this.mockRegister();

        uni.showToast({
          title: "注册成功",
          icon: "success",
        });

        // 延迟跳转到登录页面
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      } catch (error) {
        console.error("注册失败:", error);
        uni.showToast({
          title: error.message || "注册失败",
          icon: "none",
        });
      } finally {
        this.isRegistering = false;
      }
    },

    // 模拟注册请求
    mockRegister() {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          // 模拟手机号已存在检查
          if (this.registerForm.phone === "13800138000") {
            reject(new Error("该手机号已被注册"));
          } else {
            resolve();
          }
        }, 1000);
      });
    },

    // 跳转到登录页面
    goToLogin() {
      uni.navigateBack();
    },
  },
};
</script>

<style scoped>
/* 页面容器 */
.register-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: -50rpx;
  animation-delay: 0s;
}

.circle-2 {
  width: 150rpx;
  height: 150rpx;
  top: 60%;
  left: -30rpx;
  animation-delay: 2s;
}

.circle-3 {
  width: 100rpx;
  height: 100rpx;
  top: 30%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20rpx) rotate(180deg);
  }
}

/* 顶部区域 */
.header-section {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 40rpx 20rpx;
}

.logo-container {
  position: relative;
  margin-bottom: 20rpx;
}

.logo {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  position: relative;
  z-index: 2;
}

.logo-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120rpx;
  height: 120rpx;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.3) 0%,
    transparent 70%
  );
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.7;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 1;
  }
}

.app-name {
  font-size: 40rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.3);
}

.welcome-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 6rpx;
  text-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.2);
}

.subtitle {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  text-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.2);
}

/* 表单卡片 */
.form-card {
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 40rpx 40rpx 40rpx 40rpx;
  margin: 0 20rpx;
  box-shadow: 0 -10rpx 40rpx rgba(0, 0, 0, 0.1);
  max-height: 75vh;
  overflow-y: auto;
}

.card-header {
  padding: 25rpx 40rpx 15rpx;
  text-align: center;
}

.form-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.title-underline {
  width: 45rpx;
  height: 3rpx;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 2rpx;
  margin: 0 auto;
}

.form-content {
  padding: 0 40rpx 25rpx;
}

.form-group {
  margin-bottom: 16rpx;
}

.input-container {
  position: relative;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 14rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.input-wrapper.input-focused {
  border-color: #667eea;
  background: white;
  box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.1);
  transform: translateY(-1rpx);
}

.input-wrapper.input-error {
  border-color: #ff6b6b;
  background: rgba(255, 107, 107, 0.05);
}

.input-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 70rpx;
}

.input-icon {
  font-size: 24rpx;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.input-focused .input-icon {
  opacity: 1;
}

.form-input {
  flex: 1;
  height: 70rpx;
  font-size: 24rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
}

.form-input::placeholder {
  color: #999;
  font-size: 22rpx;
}

.password-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 70rpx;
  cursor: pointer;
}

.toggle-icon {
  font-size: 24rpx;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.password-toggle:active .toggle-icon {
  opacity: 1;
}

.error-message {
  margin-top: 6rpx;
  margin-left: 14rpx;
}

.error-text {
  font-size: 18rpx;
  color: #ff6b6b;
  display: flex;
  align-items: center;
}

.error-text::before {
  content: "⚠️";
  margin-right: 4rpx;
  font-size: 14rpx;
}

/* 可选字段区域 */
.optional-fields {
  margin-top: 8rpx;
  padding-top: 8rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}

/* 协议区域 */
.agreement-section {
  margin: 20rpx 0;
  padding: 0 8rpx;
}

.agreement-wrapper {
  display: flex;
  align-items: flex-start;
  cursor: pointer;
}

.custom-checkbox {
  width: 28rpx;
  height: 28rpx;
  border: 2rpx solid #ddd;
  border-radius: 5rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10rpx;
  margin-top: 2rpx;
  transition: all 0.3s ease;
  background: white;
  flex-shrink: 0;
}

.custom-checkbox.checkbox-checked {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.checkbox-icon {
  color: white;
  font-size: 16rpx;
  font-weight: bold;
}

.agreement-text {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
  flex: 1;
}

.agreement-link {
  color: #667eea;
  font-weight: 600;
  position: relative;
}

.agreement-link::after {
  content: "";
  position: absolute;
  bottom: -1rpx;
  left: 0;
  width: 0;
  height: 1rpx;
  background: #667eea;
  transition: width 0.3s ease;
}

.agreement-link:active::after {
  width: 100%;
}

/* 注册按钮 */
.register-button {
  width: 100%;
  height: 75rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 18rpx;
  margin-bottom: 20rpx;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 5rpx 18rpx rgba(102, 126, 234, 0.25);
}

.register-button:active {
  transform: scale(0.98);
  box-shadow: 0 3rpx 12rpx rgba(102, 126, 234, 0.35);
}

.register-button.button-disabled {
  background: linear-gradient(135deg, #ccc 0%, #999 100%);
  box-shadow: 0 3rpx 12rpx rgba(0, 0, 0, 0.1);
}

.register-button.button-loading {
  pointer-events: none;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;
}

.loading-spinner {
  width: 28rpx;
  height: 28rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.button-text {
  font-size: 26rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

/* 登录区域 */
.login-section {
  text-align: center;
  padding: 12rpx 0;
}

.login-text {
  font-size: 22rpx;
  color: #666;
}

.login-link {
  font-size: 22rpx;
  color: #667eea;
  margin-left: 6rpx;
  font-weight: 600;
  position: relative;
}

.login-link::after {
  content: "";
  position: absolute;
  bottom: -2rpx;
  left: 0;
  width: 0;
  height: 2rpx;
  background: #667eea;
  transition: width 0.3s ease;
}

.login-link:active::after {
  width: 100%;
}
</style>
