<template>
	<view class="register-page">
		<!-- 顶部装饰 -->
		<view class="header-decoration">
			<image class="logo" src="/static/logo.png" mode="aspectFit"></image>
			<text class="app-name">CY商城</text>
			<text class="welcome-text">欢迎注册</text>
		</view>

		<!-- 注册表单 -->
		<view class="register-form">
			<view class="form-group">
				<view class="input-wrapper">
					<image class="input-icon" src="/static/icons/phone.png"></image>
					<input 
						class="form-input" 
						type="number" 
						placeholder="请输入手机号"
						v-model="registerForm.phone"
						@blur="validatePhone"
						maxlength="11"
					/>
				</view>
				<text class="error-text" v-if="errors.phone">{{errors.phone}}</text>
			</view>

			<view class="form-group">
				<view class="input-wrapper">
					<image class="input-icon" src="/static/icons/password.png"></image>
					<input 
						class="form-input" 
						:type="showPassword ? 'text' : 'password'" 
						placeholder="请设置密码（6-20位）"
						v-model="registerForm.password"
						@blur="validatePassword"
						maxlength="20"
					/>
					<image 
						class="password-toggle" 
						:src="showPassword ? '/static/icons/eye-open.png' : '/static/icons/eye-close.png'"
						@click="togglePassword"
					></image>
				</view>
				<text class="error-text" v-if="errors.password">{{errors.password}}</text>
			</view>

			<view class="form-group">
				<view class="input-wrapper">
					<image class="input-icon" src="/static/icons/password.png"></image>
					<input 
						class="form-input" 
						:type="showConfirmPassword ? 'text' : 'password'" 
						placeholder="请确认密码"
						v-model="registerForm.confirmPassword"
						@blur="validateConfirmPassword"
						maxlength="20"
					/>
					<image 
						class="password-toggle" 
						:src="showConfirmPassword ? '/static/icons/eye-open.png' : '/static/icons/eye-close.png'"
						@click="toggleConfirmPassword"
					></image>
				</view>
				<text class="error-text" v-if="errors.confirmPassword">{{errors.confirmPassword}}</text>
			</view>

			<view class="form-group">
				<view class="input-wrapper">
					<image class="input-icon" src="/static/icons/user.png"></image>
					<input 
						class="form-input" 
						type="text" 
						placeholder="请输入姓名（选填）"
						v-model="registerForm.name"
						@blur="validateName"
						maxlength="20"
					/>
				</view>
				<text class="error-text" v-if="errors.name">{{errors.name}}</text>
			</view>

			<view class="form-group">
				<view class="input-wrapper">
					<image class="input-icon" src="/static/icons/store.png"></image>
					<input 
						class="form-input" 
						type="text" 
						placeholder="请输入门店编号（选填）"
						v-model="registerForm.storeCode"
						@blur="validateStoreCode"
						maxlength="20"
					/>
				</view>
				<text class="error-text" v-if="errors.storeCode">{{errors.storeCode}}</text>
			</view>

			<view class="form-options">
				<view class="agreement" @click="toggleAgreement">
					<image 
						class="checkbox" 
						:src="agreedToTerms ? '/static/icons/checkbox-checked.png' : '/static/icons/checkbox-unchecked.png'"
					></image>
					<text class="agreement-text">
						我已阅读并同意
						<text class="agreement-link" @click.stop="viewUserAgreement">《用户协议》</text>
						和
						<text class="agreement-link" @click.stop="viewPrivacyPolicy">《隐私政策》</text>
					</text>
				</view>
			</view>

			<button 
				class="register-btn" 
				:class="{ 'register-btn-disabled': !canRegister }"
				:disabled="!canRegister"
				@click="handleRegister"
			>
				{{isRegistering ? '注册中...' : '立即注册'}}
			</button>

			<view class="divider">
				<view class="divider-line"></view>
				<text class="divider-text">或</text>
				<view class="divider-line"></view>
			</view>

			<view class="login-tip">
				<text class="tip-text">已有账号？</text>
				<text class="login-link" @click="goToLogin">立即登录</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 注册表单数据
				registerForm: {
					phone: '',
					password: '',
					confirmPassword: '',
					name: '',
					storeCode: ''
				},
				// 表单验证错误
				errors: {
					phone: '',
					password: '',
					confirmPassword: '',
					name: '',
					storeCode: ''
				},
				// 是否显示密码
				showPassword: false,
				showConfirmPassword: false,
				// 是否同意协议
				agreedToTerms: false,
				// 是否正在注册
				isRegistering: false
			}
		},
		computed: {
			// 是否可以注册
			canRegister() {
				return this.registerForm.phone.trim() && 
					   this.registerForm.password.trim() && 
					   this.registerForm.confirmPassword.trim() &&
					   !this.errors.phone && 
					   !this.errors.password && 
					   !this.errors.confirmPassword &&
					   this.agreedToTerms &&
					   !this.isRegistering;
			}
		},
		methods: {
			// 验证手机号
			validatePhone() {
				const phone = this.registerForm.phone.trim();
				if (!phone) {
					this.errors.phone = '请输入手机号';
					return false;
				}
				
				const phoneRegex = /^1[3-9]\d{9}$/;
				if (!phoneRegex.test(phone)) {
					this.errors.phone = '请输入正确的手机号';
					return false;
				}
				
				this.errors.phone = '';
				return true;
			},

			// 验证密码
			validatePassword() {
				const password = this.registerForm.password.trim();
				if (!password) {
					this.errors.password = '请输入密码';
					return false;
				}
				
				if (password.length < 6) {
					this.errors.password = '密码至少6位';
					return false;
				}
				
				if (password.length > 20) {
					this.errors.password = '密码最多20位';
					return false;
				}
				
				// 密码强度验证（至少包含字母和数字）
				const strongPasswordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{6,}$/;
				if (!strongPasswordRegex.test(password)) {
					this.errors.password = '密码需包含字母和数字';
					return false;
				}
				
				this.errors.password = '';
				return true;
			},

			// 验证确认密码
			validateConfirmPassword() {
				const confirmPassword = this.registerForm.confirmPassword.trim();
				if (!confirmPassword) {
					this.errors.confirmPassword = '请确认密码';
					return false;
				}
				
				if (confirmPassword !== this.registerForm.password) {
					this.errors.confirmPassword = '两次输入的密码不一致';
					return false;
				}
				
				this.errors.confirmPassword = '';
				return true;
			},

			// 验证姓名
			validateName() {
				const name = this.registerForm.name.trim();
				if (name && name.length > 20) {
					this.errors.name = '姓名最多20个字符';
					return false;
				}
				
				this.errors.name = '';
				return true;
			},

			// 验证门店编号
			validateStoreCode() {
				const storeCode = this.registerForm.storeCode.trim();
				if (storeCode && storeCode.length > 20) {
					this.errors.storeCode = '门店编号最多20个字符';
					return false;
				}
				
				this.errors.storeCode = '';
				return true;
			},

			// 切换密码显示
			togglePassword() {
				this.showPassword = !this.showPassword;
			},

			// 切换确认密码显示
			toggleConfirmPassword() {
				this.showConfirmPassword = !this.showConfirmPassword;
			},

			// 切换协议同意状态
			toggleAgreement() {
				this.agreedToTerms = !this.agreedToTerms;
			},

			// 查看用户协议
			viewUserAgreement() {
				uni.navigateTo({
					url: '/pages/agreement/user'
				});
			},

			// 查看隐私政策
			viewPrivacyPolicy() {
				uni.navigateTo({
					url: '/pages/privacy/privacy'
				});
			},

			// 处理注册
			async handleRegister() {
				if (!this.validatePhone() || !this.validatePassword() || !this.validateConfirmPassword()) {
					return;
				}

				if (!this.agreedToTerms) {
					uni.showToast({
						title: '请先同意用户协议和隐私政策',
						icon: 'none'
					});
					return;
				}

				this.isRegistering = true;

				try {
					// TODO: 调用注册API
					console.log('注册请求:', this.registerForm);
					
					// 模拟注册请求
					await this.mockRegister();
					
					uni.showToast({
						title: '注册成功',
						icon: 'success'
					});

					// 延迟跳转到登录页面
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);

				} catch (error) {
					console.error('注册失败:', error);
					uni.showToast({
						title: error.message || '注册失败',
						icon: 'none'
					});
				} finally {
					this.isRegistering = false;
				}
			},

			// 模拟注册请求
			mockRegister() {
				return new Promise((resolve, reject) => {
					setTimeout(() => {
						// 模拟手机号已存在检查
						if (this.registerForm.phone === '13800138000') {
							reject(new Error('该手机号已被注册'));
						} else {
							resolve();
						}
					}, 1000);
				});
			},

			// 跳转到登录页面
			goToLogin() {
				uni.navigateBack();
			}
		}
	}
</script>

<style scoped>
	.register-page {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 0 60rpx;
		display: flex;
		flex-direction: column;
	}

	/* 顶部装饰 */
	.header-decoration {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 120rpx 0 60rpx;
	}

	.logo {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 30rpx;
	}

	.app-name {
		font-size: 48rpx;
		font-weight: bold;
		color: white;
		margin-bottom: 20rpx;
	}

	.welcome-text {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.8);
	}

	/* 注册表单 */
	.register-form {
		flex: 1;
		background-color: white;
		border-radius: 30rpx 30rpx 0 0;
		padding: 60rpx 40rpx 40rpx;
		margin-top: 40rpx;
	}

	.form-group {
		margin-bottom: 30rpx;
	}

	.input-wrapper {
		position: relative;
		display: flex;
		align-items: center;
		background-color: #f8f9fa;
		border-radius: 15rpx;
		padding: 0 20rpx;
		border: 2rpx solid transparent;
		transition: all 0.3s ease;
	}

	.input-wrapper:focus-within {
		border-color: #667eea;
		background-color: white;
		box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
	}

	.input-icon {
		width: 32rpx;
		height: 32rpx;
		margin-right: 20rpx;
	}

	.form-input {
		flex: 1;
		height: 88rpx;
		font-size: 28rpx;
		color: #333;
		background-color: transparent;
	}

	.password-toggle {
		width: 32rpx;
		height: 32rpx;
		margin-left: 20rpx;
	}

	.error-text {
		font-size: 24rpx;
		color: #ff4757;
		margin-top: 10rpx;
		margin-left: 20rpx;
	}

	/* 表单选项 */
	.form-options {
		margin-bottom: 50rpx;
	}

	.agreement {
		display: flex;
		align-items: flex-start;
	}

	.checkbox {
		width: 32rpx;
		height: 32rpx;
		margin-right: 15rpx;
		margin-top: 5rpx;
		flex-shrink: 0;
	}

	.agreement-text {
		font-size: 24rpx;
		color: #666;
		line-height: 1.5;
		flex: 1;
	}

	.agreement-link {
		color: #667eea;
		text-decoration: underline;
	}

	/* 注册按钮 */
	.register-btn {
		width: 100%;
		height: 88rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
		font-size: 32rpx;
		font-weight: bold;
		border-radius: 15rpx;
		border: none;
		margin-bottom: 40rpx;
		transition: all 0.3s ease;
	}

	.register-btn:active {
		transform: scale(0.98);
	}

	.register-btn-disabled {
		background: #ccc;
		color: #999;
	}

	/* 分割线 */
	.divider {
		display: flex;
		align-items: center;
		margin-bottom: 40rpx;
	}

	.divider-line {
		flex: 1;
		height: 1rpx;
		background-color: #e0e0e0;
	}

	.divider-text {
		font-size: 24rpx;
		color: #999;
		margin: 0 20rpx;
	}

	/* 登录提示 */
	.login-tip {
		text-align: center;
	}

	.tip-text {
		font-size: 26rpx;
		color: #666;
	}

	.login-link {
		font-size: 26rpx;
		color: #667eea;
		margin-left: 10rpx;
	}
</style>
