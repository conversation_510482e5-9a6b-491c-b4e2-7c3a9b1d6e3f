<template>
  <view class="lucky-draw-page">
    <!-- 顶部导航 -->
    <view class="navbar">
      <view class="nav-left" @click="goBack">
        <text class="nav-icon">←</text>
      </view>
      <text class="nav-title">幸运大抽奖</text>
      <view class="nav-right"></view>
    </view>

    <!-- 抽奖背景装饰 -->
    <view class="background-decoration">
      <view class="star star-1">⭐</view>
      <view class="star star-2">✨</view>
      <view class="star star-3">🌟</view>
      <view class="star star-4">💫</view>
      <view class="star star-5">⭐</view>
      <view class="star star-6">✨</view>
    </view>

    <!-- 抽奖转盘 -->
    <view class="lottery-container">
      <view class="lottery-wheel" :class="{ spinning: isSpinning }">
        <view class="wheel-center">
          <text class="center-text">抽奖</text>
        </view>
        <view
          class="wheel-item"
          v-for="(prize, index) in prizes"
          :key="index"
          :style="getItemStyle(index)"
        >
          <text class="prize-text">{{ prize.name }}</text>
        </view>
      </view>
      <view class="lottery-pointer">
        <text class="pointer-icon">📍</text>
      </view>
    </view>

    <!-- 抽奖按钮 -->
    <view class="draw-button-container">
      <button
        class="draw-button"
        :disabled="!canDraw || isSpinning"
        @click="startDraw"
      >
        <text class="button-text">{{ buttonText }}</text>
      </button>
      <view class="draw-info">
        <text class="info-text">今日剩余抽奖次数：{{ remainingTimes }}</text>
      </view>
    </view>

    <!-- 奖品展示 -->
    <view class="prizes-display">
      <view class="section-title">
        <text class="title-text">丰厚奖品</text>
      </view>
      <view class="prizes-grid">
        <view class="prize-item" v-for="(prize, index) in prizes" :key="index">
          <image
            class="prize-image"
            :src="prize.image"
            mode="aspectFill"
          ></image>
          <text class="prize-name">{{ prize.name }}</text>
          <text class="prize-desc">{{ prize.description }}</text>
        </view>
      </view>
    </view>

    <!-- 中奖记录 -->
    <view class="winning-records">
      <view class="section-title">
        <text class="title-text">中奖记录</text>
      </view>
      <view class="records-list">
        <view
          class="record-item"
          v-for="(record, index) in winningRecords"
          :key="index"
        >
          <text class="record-user">{{ record.user }}</text>
          <text class="record-prize">{{ record.prize }}</text>
          <text class="record-time">{{ record.time }}</text>
        </view>
      </view>
    </view>

    <!-- 抽奖结果弹窗 -->
    <view class="result-modal" v-if="showResult" @click="closeResult">
      <view class="modal-content" @click.stop>
        <view class="result-header">
          <text class="result-title">🎉 恭喜您！</text>
        </view>
        <view class="result-body">
          <image
            class="result-image"
            :src="currentPrize.image"
            mode="aspectFill"
          ></image>
          <text class="result-name">{{ currentPrize.name }}</text>
          <text class="result-desc">{{ currentPrize.description }}</text>
        </view>
        <view class="result-footer">
          <button class="confirm-button" @click="closeResult">
            <text class="confirm-text">确定</text>
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      isSpinning: false,
      showResult: false,
      remainingTimes: 3,
      currentPrize: {},
      prizes: [
        {
          id: 1,
          name: "iPhone15 Pro",
          description: "256GB 深空黑色",
          image:
            "https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=300&h=300&fit=crop",
          probability: 0.01,
        },
        {
          id: 2,
          name: "华为Mate60",
          description: "512GB 雅川青",
          image:
            "https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=300&h=300&fit=crop",
          probability: 0.01,
        },
        {
          id: 3,
          name: "100元购物券",
          description: "全场通用无门槛",
          image:
            "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=300&h=300&fit=crop",
          probability: 0.1,
        },
        {
          id: 4,
          name: "50元购物券",
          description: "满200元可用",
          image:
            "https://images.unsplash.com/photo-1567581935884-3349723552ca?w=300&h=300&fit=crop",
          probability: 0.2,
        },
        {
          id: 5,
          name: "20元购物券",
          description: "满100元可用",
          image:
            "https://images.unsplash.com/photo-1580910051074-3eb694886505?w=300&h=300&fit=crop",
          probability: 0.3,
        },
        {
          id: 6,
          name: "谢谢参与",
          description: "下次再来",
          image:
            "https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=300&h=300&fit=crop",
          probability: 0.38,
        },
      ],
      winningRecords: [
        { user: "138****8888", prize: "iPhone15 Pro", time: "2分钟前" },
        { user: "139****9999", prize: "100元购物券", time: "5分钟前" },
        { user: "136****6666", prize: "华为Mate60", time: "8分钟前" },
        { user: "137****7777", prize: "50元购物券", time: "12分钟前" },
        { user: "135****5555", prize: "20元购物券", time: "15分钟前" },
      ],
    };
  },

  computed: {
    canDraw() {
      return this.remainingTimes > 0;
    },

    buttonText() {
      if (this.isSpinning) {
        return "抽奖中...";
      } else if (!this.canDraw) {
        return "今日次数已用完";
      } else {
        return "开始抽奖";
      }
    },
  },

  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 获取奖品项样式
    getItemStyle(index) {
      const angle = (360 / this.prizes.length) * index;
      return {
        transform: `rotate(${angle}deg)`,
        transformOrigin: "50% 200rpx",
      };
    },

    // 开始抽奖
    startDraw() {
      if (!this.canDraw || this.isSpinning) return;

      this.isSpinning = true;
      this.remainingTimes--;

      // 模拟抽奖逻辑
      const randomPrize = this.getRandomPrize();
      this.currentPrize = randomPrize;

      // 抽奖动画持续3秒
      setTimeout(() => {
        this.isSpinning = false;
        this.showResult = true;

        // 添加中奖记录
        this.addWinningRecord(randomPrize);
      }, 3000);
    },

    // 获取随机奖品
    getRandomPrize() {
      const random = Math.random();
      let cumulativeProbability = 0;

      for (let prize of this.prizes) {
        cumulativeProbability += prize.probability;
        if (random <= cumulativeProbability) {
          return prize;
        }
      }

      return this.prizes[this.prizes.length - 1]; // 默认返回最后一个
    },

    // 添加中奖记录
    addWinningRecord(prize) {
      const newRecord = {
        user:
          "138****" +
          Math.floor(Math.random() * 10000)
            .toString()
            .padStart(4, "0"),
        prize: prize.name,
        time: "刚刚",
      };

      this.winningRecords.unshift(newRecord);
      if (this.winningRecords.length > 10) {
        this.winningRecords.pop();
      }
    },

    // 关闭结果弹窗
    closeResult() {
      this.showResult = false;
    },
  },
};
</script>

<style scoped>
.lucky-draw-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

/* 导航栏 */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 88rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  z-index: 1000;
}

.nav-left,
.nav-right {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
}

.nav-icon {
  font-size: 32rpx;
  color: white;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.star {
  position: absolute;
  font-size: 30rpx;
  animation: twinkle 3s ease-in-out infinite;
}

.star-1 {
  top: 15%;
  left: 10%;
  animation-delay: 0s;
}
.star-2 {
  top: 25%;
  right: 15%;
  animation-delay: 0.5s;
}
.star-3 {
  top: 45%;
  left: 5%;
  animation-delay: 1s;
}
.star-4 {
  top: 65%;
  right: 10%;
  animation-delay: 1.5s;
}
.star-5 {
  top: 80%;
  left: 20%;
  animation-delay: 2s;
}
.star-6 {
  top: 35%;
  right: 25%;
  animation-delay: 2.5s;
}

@keyframes twinkle {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* 抽奖转盘 */
.lottery-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 150rpx;
  margin-bottom: 60rpx;
}

.lottery-wheel {
  width: 400rpx;
  height: 400rpx;
  border-radius: 50%;
  background: white;
  position: relative;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
  transition: transform 3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.lottery-wheel.spinning {
  transform: rotate(1800deg);
}

.wheel-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.2);
}

.center-text {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.wheel-item {
  position: absolute;
  top: 20rpx;
  left: 50%;
  transform-origin: 50% 180rpx;
  width: 80rpx;
  height: 160rpx;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 20rpx;
}

.prize-text {
  font-size: 20rpx;
  color: #333;
  font-weight: 600;
  text-align: center;
  line-height: 1.2;
  transform: rotate(-90deg);
  white-space: nowrap;
}

.lottery-pointer {
  position: absolute;
  top: -20rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 20;
}

.pointer-icon {
  font-size: 40rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.3));
}

/* 抽奖按钮 */
.draw-button-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60rpx;
}

.draw-button {
  width: 300rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8e53);
  border: none;
  border-radius: 50rpx;
  box-shadow: 0 8rpx 25rpx rgba(255, 107, 107, 0.4);
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
}

.draw-button:disabled {
  background: #ccc;
  box-shadow: none;
}

.draw-button:active:not(:disabled) {
  transform: scale(0.95);
}

.button-text {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.draw-info {
  background: rgba(255, 255, 255, 0.2);
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.info-text {
  color: white;
  font-size: 24rpx;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.3);
}

/* 奖品展示 */
.prizes-display,
.winning-records {
  background: rgba(255, 255, 255, 0.95);
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  backdrop-filter: blur(10rpx);
}

.section-title {
  text-align: center;
  margin-bottom: 30rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.prizes-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.prize-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 15rpx;
}

.prize-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 10rpx;
  margin-bottom: 10rpx;
}

.prize-name {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 5rpx;
}

.prize-desc {
  font-size: 20rpx;
  color: #666;
  text-align: center;
}

/* 中奖记录 */
.records-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.record-user {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.record-prize {
  font-size: 24rpx;
  color: #667eea;
  font-weight: 600;
}

.record-time {
  font-size: 20rpx;
  color: #999;
}

/* 结果弹窗 */
.result-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal-content {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin: 40rpx;
  text-align: center;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
}

.result-header {
  margin-bottom: 30rpx;
}

.result-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.result-body {
  margin-bottom: 40rpx;
}

.result-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
}

.result-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.result-desc {
  font-size: 24rpx;
  color: #666;
}

.confirm-button {
  width: 200rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
}

.confirm-text {
  color: white;
  font-size: 28rpx;
  font-weight: 600;
}
</style>
